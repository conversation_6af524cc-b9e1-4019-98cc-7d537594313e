/* Custom styles for attendance management frontend */

* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    margin: 0;
    padding: 0;
    min-height: 100vh;
}

.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    color: white;
    padding: 15px 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.header h1 {
    font-size: 1.8rem;
    margin: 0;
    font-weight: 300;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.current-time {
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 12px;
    border-radius: 20px;
    font-family: 'Courier New', monospace;
    font-weight: bold;
}

.settings-panel {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: white;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    transition: right 0.3s ease;
    z-index: 1000;
    display: none;
}

.settings-panel.active {
    right: 0;
}

.settings-content {
    padding: 20px;
    height: 100%;
    overflow-y: auto;
}

.settings-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.main-content {
    flex: 1;
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

.card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    font-size: 1.2rem;
    margin: 0;
    font-weight: 500;
}

.card-content {
    padding: 20px;
}

.employee-selector {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.employee-info {
    margin-top: 15px;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    padding: 15px;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.form-group {
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
}

.form-group label {
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
}

.form-group input,
.form-group select {
    padding: 12px;
    border-radius: 8px;
    border: 2px solid #e1e5e9;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.clock-section {
    display: flex;
    gap: 15px;
    margin-top: 15px;
}

.clock-btn {
    flex: 1;
    padding: 25px;
    font-size: 1.1rem;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    color: white;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.clock-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.clock-in {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.clock-out {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    color: white;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.4);
}

.btn-icon {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    cursor: pointer;
    font-size: 1.2rem;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.btn-icon:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.btn-small {
    padding: 8px 12px;
    font-size: 0.9rem;
}

.btn-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
    transition: color 0.3s ease;
}

.btn-close:hover {
    color: #333;
}

.message-container {
    margin-bottom: 20px;
}

.error {
    color: #721c24;
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #dc3545;
    margin-bottom: 15px;
}

.success {
    color: #155724;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #28a745;
    margin-bottom: 15px;
}

.info {
    color: #0c5460;
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #17a2b8;
    margin-bottom: 15px;
}

.loading {
    text-align: center;
    font-style: italic;
    color: #666;
    padding: 20px;
}

.current-status {
    font-weight: bold;
    font-size: 0.9rem;
}

.attendance-list {
    max-height: 400px;
    overflow-y: auto;
    padding-right: 5px;
}

.attendance-list::-webkit-scrollbar {
    width: 6px;
}

.attendance-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.attendance-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.attendance-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.attendance-card {
    background: rgba(255, 255, 255, 0.7);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid #667eea;
    transition: all 0.3s ease;
}

.attendance-card:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateX(5px);
}

.export-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 20px;
}

.export-actions {
    display: flex;
    gap: 10px;
    grid-column: 1 / -1;
}

.footer {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    text-align: center;
    padding: 15px;
    color: white;
    margin-top: auto;
}

.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-weight: 500;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    padding: 15px 25px;
    background: #f8f9fa;
    text-align: right;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* Responsive design */
@media (max-width: 768px) {
    .main-content {
        padding: 10px;
    }
    
    .clock-section {
        flex-direction: column;
    }
    
    .export-options {
        grid-template-columns: 1fr;
    }
    
    .export-actions {
        flex-direction: column;
    }
    
    .header-content {
        flex-direction: column;
        gap: 10px;
    }
    
    .settings-panel {
        width: 100%;
        right: -100%;
    }
}
