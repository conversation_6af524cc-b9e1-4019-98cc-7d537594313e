version: '2'
services:
  db:
    image: postgres:16
    user: root
    environment:
      - POSTGRES_USER=odoo
      - POSTGRES_PASSWORD=odoo17@2023
      - POSTGRES_DB=postgres
    restart: always             # run as a service
    volumes:
        - ./postgresql:/var/lib/postgresql/data

  odoo17:
    image: odoo:17
    user: root
    depends_on:
      - db
    ports:
      - "10017:8069"
      - "20017:8072" # live chat
    tty: true
    command: --
    environment:
      - HOST=db
      - USER=odoo
      - PASSWORD=odoo17@2023
    volumes:
      #- /etc/timezone:/etc/timezone:ro
      #- /etc/localtime:/etc/localtime:ro
      - ./entrypoint.sh:/entrypoint.sh
      - ./addons:/mnt/extra-addons
      - ./etc:/etc/odoo
    restart: always             # run as a service
    