2025-06-30 07:16:07,354 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Base: Auto-vacuum internal data`. 
2025-06-30 07:16:08,103 1 INFO testdatabase odoo.addons.base.models.ir_attachment: filestore gc 0 checked, 0 removed 
2025-06-30 07:16:08,399 1 INFO testdatabase odoo.addons.base.models.res_users: GC'd 0 user log entries 
2025-06-30 07:16:08,597 1 INFO testdatabase odoo.addons.auth_totp.models.auth_totp: GC'd 0 totp devices entries 
2025-06-30 07:16:08,719 1 INFO testdatabase odoo.models.unlink: User #1 deleted bus.presence records with IDs: [2] 
2025-06-30 07:16:09,236 1 INFO testdatabase odoo.addons.sms.models.sms_sms: GC'd 0 sms marked for deletion 
2025-06-30 07:16:09,829 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Base: Auto-vacuum internal data` (2.474s). 
2025-06-30 07:16:09,847 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Base: Portal Users Deletion`. 
2025-06-30 07:16:09,857 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Base: Portal Users Deletion` (0.009s). 
2025-06-30 07:21:10,141 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: send web push notification`. 
2025-06-30 07:21:10,326 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: send web push notification` (0.095s). 
2025-06-30 07:21:10,338 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Discuss: channel member unmute`. 
2025-06-30 07:21:10,344 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Discuss: channel member unmute` (0.006s). 
2025-06-30 07:21:10,356 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Users: Notify About Unregistered Users`. 
2025-06-30 07:21:10,373 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Users: Notify About Unregistered Users` (0.016s). 
2025-06-30 07:21:10,385 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Calendar: Event Reminder`. 
2025-06-30 07:21:10,406 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Calendar: Event Reminder` (0.020s). 
2025-06-30 07:21:10,443 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-06-30 07:21:10,461 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.009s). 
2025-06-30 07:21:10,494 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-06-30 07:21:10,503 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.008s). 
2025-06-30 07:21:10,516 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-06-30 07:21:10,536 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.020s). 
2025-06-30 07:21:10,549 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-06-30 07:21:10,555 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.006s). 
2025-06-30 07:21:10,568 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Delete Notifications older than 6 Month`. 
2025-06-30 07:21:10,587 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Delete Notifications older than 6 Month` (0.018s). 
2025-06-30 07:21:10,598 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-06-30 07:21:10,609 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.010s). 
2025-06-30 07:21:10,627 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-06-30 07:21:10,646 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.018s). 
2025-06-30 07:57:01,970 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-06-30 07:57:01,999 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.026s). 
2025-06-30 08:21:13,484 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-06-30 08:21:13,500 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.013s). 
2025-06-30 08:21:13,511 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-06-30 08:21:13,522 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.010s). 
2025-06-30 08:21:13,532 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-06-30 08:21:13,557 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.024s). 
2025-06-30 08:21:13,567 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-06-30 08:21:13,575 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.007s). 
2025-06-30 08:21:13,585 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-06-30 08:21:13,596 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.010s). 
2025-06-30 08:21:13,606 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-06-30 08:21:13,614 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.007s). 
2025-06-30 08:57:04,492 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-06-30 08:57:04,505 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.010s). 
2025-06-30 09:21:16,576 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Digest Emails`. 
2025-06-30 09:21:16,676 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Digest Emails` (0.098s). 
2025-06-30 09:21:16,687 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-06-30 09:21:16,692 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.004s). 
2025-06-30 09:21:16,700 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-06-30 09:21:16,707 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.006s). 
2025-06-30 09:21:16,715 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-06-30 09:21:16,731 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.016s). 
2025-06-30 09:21:16,741 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-06-30 09:21:16,746 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.005s). 
2025-06-30 09:21:16,754 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-06-30 09:21:16,762 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.007s). 
2025-06-30 09:21:16,770 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-06-30 09:21:16,777 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.006s). 
2025-06-30 09:57:07,188 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-06-30 09:57:07,201 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.012s). 
2025-06-30 10:31:29,351 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-06-30 10:31:29,482 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-06-30 10:31:29,489 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.038s). 
2025-06-30 10:31:29,523 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-06-30 10:31:29,547 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.063s). 
2025-06-30 10:31:29,562 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.037s). 
2025-06-30 10:31:29,686 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-06-30 10:31:29,706 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-06-30 10:31:29,714 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.028s). 
2025-06-30 10:31:29,721 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.013s). 
2025-06-30 10:31:29,844 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-06-30 10:31:30,392 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.547s). 
2025-06-30 10:56:35,511 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-06-30 10:56:35,526 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.013s). 
2025-06-30 10:59:29,483 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Website Visitor : clean inactive visitors`. 
2025-06-30 10:59:29,580 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Website Visitor : clean inactive visitors` (0.094s). 
2025-06-30 11:01:29,653 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `HR Employee: check work permit validity`. 
2025-06-30 11:01:29,762 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `HR Employee: check work permit validity` (0.107s). 
2025-06-30 11:14:54,696 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Gamification: Goal Challenge Check`. 
2025-06-30 11:14:55,412 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Gamification: Goal Challenge Check` (0.714s). 
2025-06-30 11:21:01,785 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-06-30 11:21:01,858 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.006s). 
2025-06-30 11:21:01,870 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-06-30 11:21:01,884 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.014s). 
2025-06-30 11:21:01,893 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-06-30 11:21:01,901 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.007s). 
2025-06-30 11:21:01,908 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-06-30 11:21:01,914 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.005s). 
2025-06-30 11:21:01,921 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-06-30 11:21:01,925 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.003s). 
2025-06-30 11:21:01,933 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-06-30 11:21:01,940 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.007s). 
2025-06-30 11:56:39,082 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-06-30 11:56:39,104 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.020s). 
2025-06-30 12:21:04,596 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-06-30 12:21:04,611 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.013s). 
2025-06-30 12:21:04,619 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-06-30 12:21:04,637 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.017s). 
2025-06-30 12:21:04,644 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-06-30 12:21:04,655 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.010s). 
2025-06-30 12:21:04,664 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-06-30 12:21:04,671 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.007s). 
2025-06-30 12:21:04,679 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-06-30 12:21:04,683 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.003s). 
2025-06-30 12:21:04,691 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-06-30 12:21:04,698 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.006s). 
2025-06-30 12:56:41,790 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-06-30 12:56:41,808 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.015s). 
2025-06-30 13:39:53,983 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-06-30 13:39:54,011 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-06-30 13:39:54,072 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.065s). 
2025-06-30 13:39:54,106 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-06-30 13:39:54,112 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.088s). 
2025-06-30 13:39:54,123 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.016s). 
2025-06-30 13:39:54,147 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-06-30 13:39:54,148 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-06-30 13:39:54,163 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.013s). 
2025-06-30 13:39:54,172 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.020s). 
2025-06-30 13:39:54,201 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-06-30 13:39:54,218 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.015s). 
2025-06-30 14:29:42,313 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-06-30 14:29:42,322 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-06-30 14:29:42,503 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.187s). 
2025-06-30 14:29:42,551 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-06-30 14:29:42,552 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.227s). 
2025-06-30 14:29:42,753 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-06-30 14:29:43,245 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.691s). 
2025-06-30 14:29:43,246 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.016s). 
2025-06-30 14:29:43,267 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-06-30 14:29:43,268 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-06-30 14:29:43,280 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.011s). 
2025-06-30 14:29:43,287 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.016s). 
2025-06-30 14:29:43,309 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-06-30 14:29:43,350 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.038s). 
2025-06-30 15:14:33,657 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-06-30 15:14:33,753 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.094s). 
2025-06-30 17:16:57,753 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-06-30 17:16:57,847 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-06-30 17:16:58,244 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.311s). 
2025-06-30 17:16:58,278 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.354s). 
2025-06-30 17:16:58,282 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-06-30 17:16:58,315 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.031s). 
2025-06-30 17:16:58,318 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-06-30 17:16:58,333 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.012s). 
2025-06-30 17:16:58,355 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-06-30 17:16:58,380 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.023s). 
2025-06-30 17:16:58,439 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-06-30 17:16:58,477 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.036s). 
2025-06-30 17:16:58,543 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-06-30 17:16:58,577 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.032s). 
2025-06-30 20:18:43,280 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-06-30 20:18:43,316 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.030s). 
2025-06-30 20:18:43,334 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-06-30 20:18:43,348 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.013s). 
2025-06-30 20:18:43,367 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-06-30 20:18:43,382 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.014s). 
2025-06-30 20:18:43,400 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-06-30 20:18:43,418 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.018s). 
2025-06-30 20:18:43,436 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-06-30 20:18:43,457 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.020s). 
2025-06-30 20:18:43,476 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-06-30 20:18:43,507 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.030s). 
2025-06-30 20:18:43,522 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-06-30 20:18:43,533 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.010s). 
2025-06-30 22:43:00,165 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-06-30 22:43:00,203 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.031s). 
2025-06-30 22:43:00,222 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-06-30 22:43:00,234 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.011s). 
2025-06-30 22:43:00,248 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-06-30 22:43:00,258 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.010s). 
2025-06-30 22:43:00,270 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-06-30 22:43:00,305 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.034s). 
2025-06-30 22:43:00,326 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-06-30 22:43:00,430 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.103s). 
2025-06-30 22:43:00,445 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-06-30 22:43:00,467 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.021s). 
2025-06-30 22:43:00,488 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-06-30 22:43:00,499 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.010s). 
2025-07-01 01:15:19,342 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-01 01:15:19,377 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.029s). 
2025-07-01 01:15:19,397 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-01 01:15:19,407 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.010s). 
2025-07-01 01:15:19,421 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-01 01:15:19,430 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.008s). 
2025-07-01 01:15:19,442 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-01 01:15:19,459 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.016s). 
2025-07-01 01:15:19,472 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-01 01:15:19,484 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.011s). 
2025-07-01 01:15:19,495 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-01 01:15:19,520 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.025s). 
2025-07-01 01:15:19,530 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-01 01:15:19,540 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.009s). 
2025-07-01 02:04:33,972 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-01 02:04:33,968 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-01 02:04:34,064 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.053s). 
2025-07-01 02:04:34,071 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.065s). 
2025-07-01 02:04:34,149 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-01 02:04:34,160 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-01 02:04:34,171 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.019s). 
2025-07-01 02:04:34,248 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Account: Post draft entries with auto_post enabled and accounting date up to today`. 
2025-07-01 02:04:34,257 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.093s). 
2025-07-01 02:04:34,453 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-01 02:04:34,479 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.025s). 
2025-07-01 02:04:34,575 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-01 02:04:34,579 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Account: Post draft entries with auto_post enabled and accounting date up to today` (0.325s). 
2025-07-01 02:04:34,648 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-01 02:04:34,727 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.148s). 
2025-07-01 02:04:34,746 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.022s). 
2025-07-01 04:05:01,318 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-01 04:05:01,341 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-01 04:05:01,393 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.072s). 
2025-07-01 04:05:01,401 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.054s). 
2025-07-01 04:05:01,423 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-01 04:05:01,447 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.023s). 
2025-07-01 04:05:01,465 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-01 04:05:01,545 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.076s). 
2025-07-01 04:05:01,556 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-01 04:05:01,578 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.017s). 
2025-07-01 04:05:01,580 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-01 04:05:01,639 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-01 04:05:01,862 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.277s). 
2025-07-01 04:05:01,877 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.014s). 
2025-07-01 04:05:01,892 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Gamification: Karma tracking consolidation`. 
2025-07-01 04:05:02,002 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Gamification: Karma tracking consolidation` (0.109s). 
2025-07-01 04:22:30,828 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-01 04:22:30,837 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-01 04:22:30,914 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.075s). 
2025-07-01 04:22:30,922 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.092s). 
2025-07-01 04:22:30,950 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-01 04:22:30,987 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-01 04:22:31,002 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.050s). 
2025-07-01 04:22:31,056 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.068s). 
2025-07-01 04:22:31,090 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-01 04:22:31,102 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-01 04:22:31,637 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.543s). 
2025-07-01 04:22:32,169 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (1.064s). 
2025-07-01 04:56:34,076 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Sales: Send pending emails`. 
2025-07-01 04:56:34,142 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Sales: Send pending emails` (0.059s). 
2025-07-01 04:56:34,158 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Send invoices automatically`. 
2025-07-01 04:56:34,197 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Send invoices automatically` (0.038s). 
2025-07-01 04:57:07,965 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-01 04:57:08,092 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.125s). 
2025-07-01 05:21:33,597 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-01 05:21:33,616 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.016s). 
2025-07-01 05:21:33,630 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-01 05:21:33,644 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.012s). 
2025-07-01 05:21:33,659 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-01 05:21:33,667 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.008s). 
2025-07-01 05:21:33,679 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-01 05:21:33,688 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.009s). 
2025-07-01 05:21:33,700 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-01 05:21:33,711 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.010s). 
2025-07-01 05:21:33,727 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-01 05:21:33,752 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.025s). 
2025-07-01 05:56:37,516 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-01 05:56:37,572 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.053s). 
2025-07-01 06:21:36,578 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-01 06:21:36,591 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.010s). 
2025-07-01 06:21:36,602 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-01 06:21:36,610 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.008s). 
2025-07-01 06:21:36,620 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-01 06:21:36,629 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.009s). 
2025-07-01 06:21:36,641 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-01 06:21:36,725 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.083s). 
2025-07-01 06:21:36,740 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-01 06:21:36,754 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.012s). 
2025-07-01 06:21:36,767 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-01 06:21:36,794 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.026s). 
