2025-07-01 06:56:40,514 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-01 06:56:40,543 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.026s). 
2025-07-01 07:15:32,650 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Base: Auto-vacuum internal data`. 
2025-07-01 07:15:33,285 1 INFO testdatabase odoo.addons.base.models.ir_attachment: filestore gc 0 checked, 0 removed 
2025-07-01 07:15:33,521 1 INFO testdatabase odoo.addons.base.models.res_users: GC'd 0 user log entries 
2025-07-01 07:15:33,628 1 INFO testdatabase odoo.addons.auth_totp.models.auth_totp: GC'd 0 totp devices entries 
2025-07-01 07:15:33,713 1 INFO testdatabase odoo.models.unlink: User #1 deleted bus.bus records with IDs: [212] 
2025-07-01 07:15:34,231 1 INFO testdatabase odoo.addons.sms.models.sms_sms: GC'd 0 sms marked for deletion 
2025-07-01 07:15:34,673 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Base: Auto-vacuum internal data` (2.021s). 
2025-07-01 07:15:34,688 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Base: Portal Users Deletion`. 
2025-07-01 07:15:34,702 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Base: Portal Users Deletion` (0.013s). 
2025-07-01 07:21:41,006 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: send web push notification`. 
2025-07-01 07:21:41,082 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: send web push notification` (0.006s). 
2025-07-01 07:21:41,097 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Discuss: channel member unmute`. 
2025-07-01 07:21:41,105 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Discuss: channel member unmute` (0.007s). 
2025-07-01 07:21:41,116 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Users: Notify About Unregistered Users`. 
2025-07-01 07:21:41,214 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Users: Notify About Unregistered Users` (0.098s). 
2025-07-01 07:21:41,228 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Calendar: Event Reminder`. 
2025-07-01 07:21:41,263 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Calendar: Event Reminder` (0.035s). 
2025-07-01 07:21:41,275 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Delete Notifications older than 6 Month`. 
2025-07-01 07:21:41,296 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Delete Notifications older than 6 Month` (0.020s). 
2025-07-01 07:21:41,308 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-01 07:21:41,312 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.004s). 
2025-07-01 07:21:41,325 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-01 07:21:41,332 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.007s). 
2025-07-01 07:21:41,345 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-01 07:21:41,354 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.009s). 
2025-07-01 07:21:41,373 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-01 07:21:41,380 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.006s). 
2025-07-01 07:21:41,394 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-01 07:21:41,408 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.013s). 
2025-07-01 07:21:41,419 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-01 07:21:41,441 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.020s). 
2025-07-01 08:09:32,451 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-01 08:09:32,514 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.056s). 
2025-07-01 08:27:58,669 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-01 08:27:58,676 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-01 08:27:58,685 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.015s). 
2025-07-01 08:27:58,707 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.027s). 
2025-07-01 08:27:58,712 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-01 08:27:58,723 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.010s). 
2025-07-01 08:27:58,738 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-01 08:27:58,756 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.017s). 
2025-07-01 08:27:58,766 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-01 08:27:58,787 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-01 08:27:58,802 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.034s). 
2025-07-01 08:27:58,888 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.099s). 
2025-07-01 08:56:53,065 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-01 08:56:53,107 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.039s). 
2025-07-01 09:20:42,198 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-01 09:20:42,218 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.019s). 
2025-07-01 09:20:42,231 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-01 09:20:42,240 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.009s). 
2025-07-01 09:20:54,135 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-01 09:20:54,142 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.006s). 
2025-07-01 09:20:54,153 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-01 09:20:54,165 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.011s). 
2025-07-01 09:20:54,178 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-01 09:20:54,185 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.007s). 
2025-07-01 09:20:54,197 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Digest Emails`. 
2025-07-01 09:20:56,416 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Digest Emails` (2.219s). 
2025-07-01 09:21:43,299 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-01 09:21:43,308 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.008s). 
2025-07-01 09:56:58,166 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-01 09:56:58,217 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.042s). 
2025-07-01 10:20:44,602 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-01 10:20:44,620 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.015s). 
2025-07-01 10:20:44,630 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-01 10:20:44,802 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.171s). 
2025-07-01 10:20:59,217 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-01 10:20:59,299 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.081s). 
2025-07-01 10:20:59,314 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-01 10:20:59,326 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.012s). 
2025-07-01 10:20:59,340 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-01 10:20:59,369 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.028s). 
2025-07-01 10:20:59,380 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-01 10:20:59,385 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.004s). 
2025-07-01 10:57:01,144 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-01 10:57:01,179 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.032s). 
2025-07-01 10:59:24,901 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Website Visitor : clean inactive visitors`. 
2025-07-01 10:59:25,032 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Website Visitor : clean inactive visitors` (0.130s). 
2025-07-01 11:01:01,347 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `HR Employee: check work permit validity`. 
2025-07-01 11:01:01,466 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `HR Employee: check work permit validity` (0.114s). 
2025-07-01 11:14:41,081 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Gamification: Goal Challenge Check`. 
2025-07-01 11:14:42,011 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Gamification: Goal Challenge Check` (0.927s). 
2025-07-01 11:20:48,396 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-01 11:20:48,505 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.010s). 
2025-07-01 11:20:48,523 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-01 11:20:48,641 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.117s). 
2025-07-01 11:20:48,657 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-01 11:20:48,665 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.006s). 
2025-07-01 11:21:02,328 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-01 11:21:02,442 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.113s). 
2025-07-01 11:21:02,455 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-01 11:21:02,466 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.009s). 
2025-07-01 11:21:02,481 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-01 11:21:02,511 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.029s). 
2025-07-01 11:57:04,619 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-01 11:57:04,659 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.036s). 
2025-07-01 12:20:51,027 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-01 12:20:51,049 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.018s). 
2025-07-01 12:20:51,066 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-01 12:20:51,083 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.016s). 
2025-07-01 12:20:51,098 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-01 12:20:51,105 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.006s). 
2025-07-01 12:20:51,118 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-01 12:20:51,130 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.010s). 
2025-07-01 12:21:05,948 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-01 12:21:06,060 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.111s). 
2025-07-01 12:21:06,112 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-01 12:21:06,267 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.154s). 
2025-07-01 12:57:08,123 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-01 12:57:08,152 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.026s). 
2025-07-01 13:47:34,363 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-01 13:47:34,385 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-01 13:47:34,416 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.028s). 
2025-07-01 13:47:34,418 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.050s). 
2025-07-01 13:47:34,456 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-01 13:47:34,458 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-01 13:47:34,528 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.067s). 
2025-07-01 13:47:34,567 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-01 13:47:34,622 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.054s). 
2025-07-01 13:47:34,717 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.257s). 
2025-07-01 13:47:41,472 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-01 13:47:41,490 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.013s). 
2025-07-01 14:02:32,882 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-01 14:02:33,010 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.115s). 
2025-07-01 14:34:25,203 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-01 14:34:25,076 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-01 14:34:26,918 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (1.690s). 
2025-07-01 14:34:26,953 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (1.734s). 
2025-07-01 14:34:26,980 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-01 14:34:27,006 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.020s). 
2025-07-01 14:34:27,029 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-01 14:34:27,076 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-01 14:34:27,102 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.072s). 
2025-07-01 14:34:27,228 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.131s). 
2025-07-01 14:34:27,238 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-01 14:34:27,325 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.085s). 
2025-07-01 15:03:15,167 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-01 15:03:15,235 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.061s). 
2025-07-01 16:04:19,501 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-01 16:04:19,537 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.030s). 
2025-07-01 16:04:19,557 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-01 16:04:19,586 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.028s). 
2025-07-01 16:04:19,602 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-01 16:04:19,619 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.017s). 
2025-07-01 16:04:19,632 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-01 16:04:19,674 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.041s). 
2025-07-01 16:04:19,689 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-01 16:04:19,701 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.011s). 
2025-07-01 16:04:19,715 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-01 16:04:19,728 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.012s). 
2025-07-01 16:04:19,742 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-01 16:04:19,758 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.016s). 
2025-07-01 18:06:57,292 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-01 18:06:57,298 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-01 18:06:57,404 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.091s). 
2025-07-01 18:06:57,475 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-01 18:06:57,482 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.174s). 
2025-07-01 18:06:57,505 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.027s). 
2025-07-01 18:06:57,536 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-01 18:06:57,564 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.027s). 
2025-07-01 18:06:57,787 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-01 18:06:57,794 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-01 18:06:57,817 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.022s). 
2025-07-01 18:06:57,842 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.053s). 
2025-07-01 18:06:57,901 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-01 18:06:57,922 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.019s). 
2025-07-01 21:38:56,224 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-01 21:38:56,379 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.145s). 
2025-07-01 21:38:56,413 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-01 21:38:56,452 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.038s). 
2025-07-01 21:38:56,472 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-01 21:38:56,486 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.013s). 
2025-07-01 21:38:56,517 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-01 21:38:56,541 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.022s). 
2025-07-01 21:38:56,561 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-01 21:38:56,581 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.019s). 
2025-07-01 21:38:56,596 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-01 21:38:56,604 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.007s). 
2025-07-01 21:38:56,640 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-01 21:38:56,653 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.012s). 
2025-07-02 01:03:22,572 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-02 01:03:22,612 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.033s). 
2025-07-02 01:03:22,633 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-02 01:03:22,669 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.035s). 
2025-07-02 01:03:22,685 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-02 01:03:22,698 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.013s). 
2025-07-02 01:03:22,712 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-02 01:03:22,727 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.014s). 
2025-07-02 01:03:22,741 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-02 01:03:22,753 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.011s). 
2025-07-02 01:03:22,766 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-02 01:03:22,773 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.006s). 
2025-07-02 01:03:22,785 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-02 01:03:22,795 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.009s). 
2025-07-02 01:50:33,875 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-02 01:50:33,884 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-02 01:50:33,940 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.049s). 
2025-07-02 01:50:33,974 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.075s). 
2025-07-02 01:50:34,010 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-02 01:50:34,018 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-02 01:50:34,050 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.037s). 
2025-07-02 01:50:34,103 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.080s). 
2025-07-02 01:50:34,133 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-02 01:50:34,154 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-02 01:50:34,168 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.031s). 
2025-07-02 01:50:34,194 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.031s). 
2025-07-02 03:51:00,207 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-02 03:51:00,215 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-02 03:51:00,245 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.036s). 
2025-07-02 03:51:00,265 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.048s). 
2025-07-02 03:51:00,282 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-02 03:51:00,302 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-02 03:51:00,307 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.019s). 
2025-07-02 03:51:00,343 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-02 03:51:00,350 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.044s). 
2025-07-02 03:51:00,360 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.016s). 
2025-07-02 03:51:00,391 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-02 03:51:00,414 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.022s). 
2025-07-02 03:51:00,466 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Account: Post draft entries with auto_post enabled and accounting date up to today`. 
2025-07-02 03:51:00,486 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-02 03:51:00,669 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.180s). 
2025-07-02 03:51:00,902 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Account: Post draft entries with auto_post enabled and accounting date up to today` (0.435s). 
2025-07-02 04:31:35,348 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-02 04:31:36,207 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-02 04:31:36,209 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.842s). 
2025-07-02 04:31:36,253 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.043s). 
2025-07-02 04:31:36,255 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-02 04:31:36,393 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.136s). 
2025-07-02 04:31:36,401 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-02 04:31:36,484 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-02 04:31:36,698 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.200s). 
2025-07-02 04:31:37,334 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.915s). 
2025-07-02 04:31:37,350 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-02 04:31:37,428 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-02 04:31:37,453 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.024s). 
2025-07-02 04:31:37,454 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.102s). 
2025-07-02 04:56:39,981 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Sales: Send pending emails`. 
2025-07-02 04:56:40,056 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Sales: Send pending emails` (0.055s). 
2025-07-02 04:56:40,066 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Send invoices automatically`. 
2025-07-02 04:56:40,094 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Send invoices automatically` (0.028s). 
2025-07-02 04:56:40,105 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-02 04:56:40,114 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.008s). 
2025-07-02 05:21:30,407 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-02 05:21:30,428 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.019s). 
2025-07-02 05:21:30,439 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-02 05:21:30,445 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.005s). 
2025-07-02 05:21:30,457 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-02 05:21:30,466 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.008s). 
2025-07-02 05:21:30,475 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-02 05:21:30,483 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.007s). 
2025-07-02 05:21:30,496 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-02 05:21:30,504 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.007s). 
2025-07-02 05:21:30,515 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-02 05:21:30,528 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.012s). 
2025-07-02 05:56:43,004 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-02 05:56:43,033 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.026s). 
2025-07-02 06:20:44,194 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-02 06:20:44,212 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.017s). 
2025-07-02 06:20:44,226 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-02 06:20:44,235 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.009s). 
2025-07-02 06:21:32,966 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-02 06:21:32,973 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.006s). 
2025-07-02 06:21:32,990 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-02 06:21:33,088 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.096s). 
2025-07-02 06:21:33,105 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-02 06:21:33,118 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.012s). 
2025-07-02 06:21:33,133 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-02 06:21:33,161 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.027s). 
