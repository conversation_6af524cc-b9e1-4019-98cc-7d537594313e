2025-07-02 06:56:45,976 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-02 06:56:46,002 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.025s). 
2025-07-02 07:15:29,747 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Base: Auto-vacuum internal data`. 
2025-07-02 07:15:30,543 1 INFO testdatabase odoo.addons.base.models.ir_attachment: filestore gc 0 checked, 0 removed 
2025-07-02 07:15:30,790 1 INFO testdatabase odoo.addons.base.models.res_users: GC'd 0 user log entries 
2025-07-02 07:15:30,983 1 INFO testdatabase odoo.addons.auth_totp.models.auth_totp: GC'd 0 totp devices entries 
2025-07-02 07:15:31,484 1 INFO testdatabase odoo.addons.sms.models.sms_sms: GC'd 0 sms marked for deletion 
2025-07-02 07:15:32,039 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Base: Auto-vacuum internal data` (2.288s). 
2025-07-02 07:15:32,051 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Base: Portal Users Deletion`. 
2025-07-02 07:15:32,063 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Base: Portal Users Deletion` (0.010s). 
2025-07-02 07:20:46,889 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: send web push notification`. 
2025-07-02 07:20:47,045 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: send web push notification` (0.041s). 
2025-07-02 07:20:47,105 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Discuss: channel member unmute`. 
2025-07-02 07:20:47,130 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Discuss: channel member unmute` (0.021s). 
2025-07-02 07:20:47,157 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Users: Notify About Unregistered Users`. 
2025-07-02 07:20:47,234 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Users: Notify About Unregistered Users` (0.075s). 
2025-07-02 07:20:47,266 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Calendar: Event Reminder`. 
2025-07-02 07:20:47,317 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Calendar: Event Reminder` (0.050s). 
2025-07-02 07:20:47,383 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Delete Notifications older than 6 Month`. 
2025-07-02 07:20:47,464 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Delete Notifications older than 6 Month` (0.080s). 
2025-07-02 07:20:47,500 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-02 07:20:47,542 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.040s). 
2025-07-02 07:20:47,564 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-02 07:20:47,703 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.138s). 
2025-07-02 07:21:38,405 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-02 07:21:38,414 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.007s). 
2025-07-02 07:21:38,432 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-02 07:21:38,586 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.154s). 
2025-07-02 07:21:38,607 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-02 07:21:38,615 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.007s). 
2025-07-02 07:21:38,630 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-02 07:21:38,659 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.028s). 
2025-07-02 07:56:49,584 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-02 07:56:49,602 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.016s). 
2025-07-02 08:35:47,258 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-02 08:35:47,265 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-02 08:35:47,288 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.022s). 
2025-07-02 08:35:47,292 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.031s). 
2025-07-02 08:35:47,663 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-02 08:35:47,676 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.012s). 
2025-07-02 08:35:47,695 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-02 08:35:47,707 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-02 08:35:47,726 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.017s). 
2025-07-02 08:35:47,728 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.028s). 
2025-07-02 08:35:47,756 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-02 08:35:47,802 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.045s). 
2025-07-02 08:57:26,346 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-02 08:57:26,364 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.016s). 
2025-07-02 09:20:54,309 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Digest Emails`. 
2025-07-02 09:20:54,326 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Digest Emails` (0.015s). 
2025-07-02 09:20:54,335 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-02 09:20:54,341 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.005s). 
2025-07-02 09:20:54,350 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-02 09:20:54,359 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.008s). 
2025-07-02 09:20:54,367 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-02 09:20:54,371 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.003s). 
2025-07-02 09:20:54,380 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-02 09:20:54,385 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.005s). 
2025-07-02 09:20:54,393 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-02 09:20:54,400 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.006s). 
2025-07-02 09:21:27,542 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-02 09:21:27,565 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.020s). 
2025-07-02 09:57:29,406 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-02 09:57:29,424 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.015s). 
2025-07-02 10:20:57,077 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-02 10:20:57,090 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.011s). 
2025-07-02 10:20:57,099 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-02 10:20:57,107 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.007s). 
2025-07-02 10:20:57,116 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-02 10:20:57,126 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.009s). 
2025-07-02 10:20:57,195 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-02 10:20:57,222 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.026s). 
2025-07-02 10:20:57,231 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-02 10:20:57,241 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.009s). 
2025-07-02 10:20:57,251 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-02 10:20:57,267 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.014s). 
2025-07-02 10:57:32,339 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-02 10:57:32,356 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.016s). 
2025-07-02 10:59:32,485 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Website Visitor : clean inactive visitors`. 
2025-07-02 10:59:32,597 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Website Visitor : clean inactive visitors` (0.111s). 
2025-07-02 11:01:32,655 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `HR Employee: check work permit validity`. 
2025-07-02 11:01:32,774 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `HR Employee: check work permit validity` (0.116s). 
2025-07-02 11:14:33,412 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Gamification: Goal Challenge Check`. 
2025-07-02 11:14:34,171 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Gamification: Goal Challenge Check` (0.755s). 
2025-07-02 11:20:59,172 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-02 11:20:59,252 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.004s). 
2025-07-02 11:20:59,289 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-02 11:20:59,319 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.026s). 
2025-07-02 11:20:59,336 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-02 11:20:59,347 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.011s). 
2025-07-02 11:20:59,360 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-02 11:20:59,382 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.021s). 
2025-07-02 11:20:59,393 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-02 11:20:59,400 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.007s). 
2025-07-02 11:20:59,410 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-02 11:20:59,421 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.011s). 
2025-07-02 12:09:27,823 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-02 12:09:27,860 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.034s). 
2025-07-02 12:21:29,072 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-02 12:21:29,087 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.012s). 
2025-07-02 12:21:29,096 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-02 12:21:29,106 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.009s). 
2025-07-02 12:21:29,115 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-02 12:21:29,125 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.010s). 
2025-07-02 12:21:29,134 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-02 12:21:29,160 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.024s). 
2025-07-02 12:21:29,171 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-02 12:21:29,177 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.006s). 
2025-07-02 12:21:29,188 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-02 12:21:29,196 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.007s). 
2025-07-02 13:24:21,073 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-02 13:24:21,079 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-02 13:24:21,111 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.035s). 
2025-07-02 13:24:21,114 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.034s). 
2025-07-02 13:24:21,145 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-02 13:24:21,184 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-02 13:24:21,217 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.072s). 
2025-07-02 13:24:21,240 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.055s). 
2025-07-02 13:24:21,247 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-02 13:24:21,273 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.025s). 
2025-07-02 13:24:21,274 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-02 13:24:21,305 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.028s). 
2025-07-02 13:24:21,318 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-02 13:24:21,331 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.010s). 
2025-07-02 14:54:03,048 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-02 14:54:03,055 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-07-02 14:54:03,366 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `SMS: SMS Queue Manager` (0.316s). 
2025-07-02 14:54:03,370 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Snailmail: process letters queue` (0.314s). 
2025-07-02 14:54:03,394 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `CRM: enrich leads (IAP)`. 
2025-07-02 14:54:03,431 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-07-02 14:54:03,436 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `CRM: enrich leads (IAP)` (0.041s). 
2025-07-02 14:54:03,456 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Notification: Send scheduled message notifications` (0.024s). 
2025-07-02 14:54:03,464 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-07-02 14:54:03,486 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `eCommerce: send email to customers about their abandoned cart`. 
2025-07-02 14:54:03,489 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Mail: Email Queue Manager` (0.023s). 
2025-07-02 14:54:03,521 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `eCommerce: send email to customers about their abandoned cart` (0.034s). 
2025-07-02 14:54:03,537 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-07-02 14:54:03,554 1 INFO testdatabase odoo.addons.base.models.ir_cron: Job done: `Partner Autocomplete: Sync with remote DB` (0.015s). 
2025-07-02 15:48:28,747 1 WARNING ? odoo.service.server: Thread <Thread(odoo.service.cron.cron0, started daemon 140544950928960)> virtual real time limit (3201/120s) reached. 
2025-07-02 15:48:32,954 1 WARNING ? odoo.service.server: Thread <Thread(odoo.service.cron.cron1, started daemon 140544940443200)> virtual real time limit (3205/120s) reached. 
2025-07-02 15:48:32,957 1 INFO ? odoo.service.server: Dumping stacktrace of limit exceeding threads before reloading 
2025-07-02 15:48:33,782 1 INFO ? odoo.tools.misc: 
# Thread: <Thread(odoo.service.cron.cron1, started daemon 140544940443200)> (db:testdatabase) (uid:n/a) (url:n/a) (qc:n/a qt:n/a pt:n/a)
File: "/usr/lib/python3.10/threading.py", line 973, in _bootstrap
  self._bootstrap_inner()
File: "/usr/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
  self.run()
File: "/usr/lib/python3.10/threading.py", line 953, in run
  self._target(*self._args, **self._kwargs)
File: "/usr/lib/python3/dist-packages/odoo/service/server.py", line 553, in target
  self.cron_thread(i)
File: "/usr/lib/python3/dist-packages/odoo/service/server.py", line 535, in cron_thread
  _run_cron(cr)
File: "/usr/lib/python3/dist-packages/odoo/service/server.py", line 528, in _run_cron
  ir_cron._process_jobs(db_name)
File: "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_cron.py", line 139, in _process_jobs
  registry[cls._name]._process_job(db, cron_cr, job)
File: "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_cron.py", line 321, in _process_job
  with cls.pool.cursor() as job_cr:
File: "/usr/lib/python3/dist-packages/odoo/modules/registry.py", line 946, in cursor
  return self._db.cursor()
File: "/usr/lib/python3/dist-packages/odoo/sql_db.py", line 764, in cursor
  return Cursor(self.__pool, self.__dbname, self.__dsn)
File: "/usr/lib/python3/dist-packages/odoo/sql_db.py", line 267, in __init__
  self._cnx = pool.borrow(dsn)
File: "<decorator-gen-13>", line 2, in borrow
File: "/usr/lib/python3/dist-packages/odoo/tools/func.py", line 86, in locked
  with getattr(inst, lock_attr):

# Thread: <Thread(odoo.service.cron.cron0, started daemon 140544950928960)> (db:testdatabase) (uid:n/a) (url:n/a) (qc:n/a qt:n/a pt:n/a)
File: "/usr/lib/python3.10/threading.py", line 973, in _bootstrap
  self._bootstrap_inner()
File: "/usr/lib/python3.10/threading.py", line 1016, in _bootstrap_inner
  self.run()
File: "/usr/lib/python3.10/threading.py", line 953, in run
  self._target(*self._args, **self._kwargs)
File: "/usr/lib/python3/dist-packages/odoo/service/server.py", line 553, in target
  self.cron_thread(i)
File: "/usr/lib/python3/dist-packages/odoo/service/server.py", line 535, in cron_thread
  _run_cron(cr)
File: "/usr/lib/python3/dist-packages/odoo/service/server.py", line 528, in _run_cron
  ir_cron._process_jobs(db_name)
File: "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_cron.py", line 139, in _process_jobs
  registry[cls._name]._process_job(db, cron_cr, job)
File: "/usr/lib/python3/dist-packages/odoo/sql_db.py", line 764, in cursor
  return Cursor(self.__pool, self.__dbname, self.__dsn)
File: "/usr/lib/python3/dist-packages/odoo/sql_db.py", line 278, in __init__
  self._now = None
File: "<decorator-gen-13>", line 2, in borrow
File: "/usr/lib/python3/dist-packages/odoo/tools/func.py", line 86, in locked
  with getattr(inst, lock_attr):
File: "/usr/lib/python3/dist-packages/odoo/sql_db.py", line 700, in borrow
  return result
File: "/usr/lib/python3/dist-packages/psycopg2/__init__.py", line 126, in connect
  return conn 
2025-07-02 15:48:33,801 1 INFO ? odoo.service.server: Initiating server reload 
2025-07-02 15:48:34,276 1 INFO testdatabase odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-07-02 15:48:34,291 1 ERROR testdatabase odoo.sql_db: bad query: SELECT "res_partner"."id", "res_partner"."website_meta_og_img", "res_partner"."seo_name"->>'en_US', "res_partner"."website_id", "res_partner"."is_published", "res_partner"."email_normalized", "res_partner"."message_bounce", "res_partner"."name", "res_partner"."complete_name", "res_partner"."date", "res_partner"."title", "res_partner"."parent_id", "res_partner"."ref", "res_partner"."lang", "res_partner"."tz", "res_partner"."user_id", "res_partner"."vat", "res_partner"."company_registry", "res_partner"."website", "res_partner"."comment", "res_partner"."active", "res_partner"."employee", "res_partner"."function", "res_partner"."type", "res_partner"."street", "res_partner"."street2", "res_partner"."zip", "res_partner"."city", "res_partner"."state_id", "res_partner"."country_id", "res_partner"."partner_latitude", "res_partner"."partner_longitude", "res_partner"."email", "res_partner"."phone", "res_partner"."mobile", "res_partner"."is_company", "res_partner"."industry_id", "res_partner"."company_id", "res_partner"."color", "res_partner"."partner_share", "res_partner"."commercial_partner_id", "res_partner"."commercial_company_name", "res_partner"."company_name", "res_partner"."create_uid", "res_partner"."create_date", "res_partner"."write_uid", "res_partner"."write_date", "res_partner"."signup_type", "res_partner"."signup_expiration", "res_partner"."calendar_last_notif_ack", "res_partner"."team_id", "res_partner"."partner_gid", "res_partner"."additional_info", "res_partner"."phone_sanitized", "res_partner"."debit_limit", "res_partner"."last_time_entries_checked", "res_partner"."invoice_warn", "res_partner"."invoice_warn_msg", "res_partner"."supplier_rank", "res_partner"."customer_rank", "res_partner"."ubl_cii_format", "res_partner"."peppol_endpoint", "res_partner"."peppol_eas", "res_partner"."website_description"->>'en_US', "res_partner"."website_short_description"->>'en_US', "res_partner"."sale_warn", "res_partner"."sale_warn_msg" FROM "res_partner" WHERE ("res_partner"."id" IN (2))
ERROR: cursor already closed 
2025-07-02 15:48:34,293 1 ERROR testdatabase odoo.addons.base.models.ir_cron: Call from cron SMS: SMS Queue Manager for server action #174 failed in Job #13 
Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_cron.py", line 393, in _callback
    self.env['ir.actions.server'].browse(server_action_id).run()
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_actions.py", line 911, in run
    action_groups = action.groups_id
  File "/usr/lib/python3/dist-packages/odoo/fields.py", line 2923, in __get__
    return super().__get__(records, owner)
  File "/usr/lib/python3/dist-packages/odoo/fields.py", line 1182, in __get__
    recs._fetch_field(self)
  File "/usr/lib/python3/dist-packages/odoo/models.py", line 3823, in _fetch_field
    self.fetch(fnames)
  File "/usr/lib/python3/dist-packages/odoo/models.py", line 3873, in fetch
    fetched = self._fetch_query(query, fields_to_fetch)
  File "/usr/lib/python3/dist-packages/odoo/models.py", line 3991, in _fetch_query
    field.read(fetched)
  File "/usr/lib/python3/dist-packages/odoo/fields.py", line 4862, in read
    records.env.cr.execute(query.select(sql_id1, sql_id2))
  File "/usr/lib/python3/dist-packages/odoo/sql_db.py", line 332, in execute
    res = self._obj.execute(query, params)
psycopg2.InterfaceError: cursor already closed
2025-07-02 15:48:34,310 1 INFO ? odoo.sql_db: ConnectionPool(used=0/count=0/max=64): Closed 9 connections  
2025-07-02 15:48:34,825 1 WARNING testdatabase odoo.addons.base.models.ir_cron: Exception in cron: 
Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_cron.py", line 393, in _callback
    self.env['ir.actions.server'].browse(server_action_id).run()
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_actions.py", line 911, in run
    action_groups = action.groups_id
  File "/usr/lib/python3/dist-packages/odoo/fields.py", line 2923, in __get__
    return super().__get__(records, owner)
  File "/usr/lib/python3/dist-packages/odoo/fields.py", line 1182, in __get__
    recs._fetch_field(self)
  File "/usr/lib/python3/dist-packages/odoo/models.py", line 3823, in _fetch_field
    self.fetch(fnames)
  File "/usr/lib/python3/dist-packages/odoo/models.py", line 3873, in fetch
    fetched = self._fetch_query(query, fields_to_fetch)
  File "/usr/lib/python3/dist-packages/odoo/models.py", line 3991, in _fetch_query
    field.read(fetched)
  File "/usr/lib/python3/dist-packages/odoo/fields.py", line 4862, in read
    records.env.cr.execute(query.select(sql_id1, sql_id2))
  File "/usr/lib/python3/dist-packages/odoo/sql_db.py", line 332, in execute
    res = self._obj.execute(query, params)
psycopg2.InterfaceError: cursor already closed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_cron.py", line 139, in _process_jobs
    registry[cls._name]._process_job(db, cron_cr, job)
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_cron.py", line 354, in _process_job
    ir_cron._callback(job['cron_name'], job['ir_actions_server_id'], job['id'])
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_cron.py", line 404, in _callback
    self._handle_callback_exception(cron_name, server_action_id, job_id, e)
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_cron.py", line 411, in _handle_callback_exception
    self._cr.rollback()
  File "/usr/lib/python3/dist-packages/odoo/sql_db.py", line 486, in rollback
    result = self._cnx.rollback()
psycopg2.InterfaceError: connection already closed
2025-07-02 15:48:34,838 1 ERROR testdatabase odoo.addons.base.models.ir_cron: Call from cron Snailmail: process letters queue for server action #184 failed in Job #14 
Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_cron.py", line 384, in _callback
    if self.pool != self.pool.check_signaling():
  File "/usr/lib/python3/dist-packages/odoo/modules/registry.py", line 837, in check_signaling
    with closing(self.cursor()) as cr:
  File "/usr/lib/python3.10/contextlib.py", line 340, in __exit__
    self.thing.close()
  File "/usr/lib/python3/dist-packages/odoo/sql_db.py", line 438, in close
    return self._close(False)
  File "/usr/lib/python3/dist-packages/odoo/sql_db.py", line 468, in _close
    self.__pool.give_back(self._cnx, keep_in_pool=keep_in_pool)
  File "<decorator-gen-14>", line 2, in give_back
  File "/usr/lib/python3/dist-packages/odoo/tools/func.py", line 87, in locked
    return func(inst, *args, **kwargs)
  File "/usr/lib/python3/dist-packages/odoo/sql_db.py", line 718, in give_back
    raise PoolError('This connection does not belong to the pool')
psycopg2.pool.PoolError: This connection does not belong to the pool
2025-07-02 15:48:46,072 1 INFO ? odoo: Odoo version 17.0-20250618 
2025-07-02 15:48:46,073 1 INFO ? odoo: Using configuration file at /etc/odoo/odoo.conf 
2025-07-02 15:48:46,075 1 INFO ? odoo: addons paths: ['/usr/lib/python3/dist-packages/odoo/addons', '/etc/odoo/addons/17.0', '/mnt/extra-addons'] 
2025-07-02 15:48:46,077 1 INFO ? odoo: database: odoo@db:5432 
2025-07-02 15:48:46,876 1 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at /usr/local/bin/wkhtmltopdf 
2025-07-02 15:48:48,592 1 INFO ? odoo.service.server: HTTP service (werkzeug) running on 545cc0025106:8069 
