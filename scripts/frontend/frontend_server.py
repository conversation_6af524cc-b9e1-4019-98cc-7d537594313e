#!/usr/bin/env python3
"""
Simple HTTP server to serve the new_frontend_v2 and provide API proxy to Odoo
"""

import http.server
import socketserver
import json
import urllib.parse
import xmlrpc.client
import os
import sys
from pathlib import Path

# Configuration
PORT = 8080
ODOO_URL = "http://localhost:10017"
ODOO_DB = "extended_attendance"
ODOO_USERNAME = "<EMAIL>"
ODOO_PASSWORD = "admin"

class OdooAPIHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        # Set the directory to serve files from
        frontend_dir = Path(__file__).parent.parent.parent / "new_frontend_v2"
        os.chdir(frontend_dir)
        super().__init__(*args, **kwargs)
    
    def do_POST(self):
        if self.path.startswith('/api/'):
            self.handle_api_request()
        else:
            self.send_error(405, "Method Not Allowed")
    
    def do_GET(self):
        if self.path.startswith('/api/'):
            self.handle_api_request()
        else:
            super().do_GET()
    
    def handle_api_request(self):
        try:
            # Connect to Odoo
            common = xmlrpc.client.ServerProxy(f'{ODOO_URL}/xmlrpc/2/common')
            models = xmlrpc.client.ServerProxy(f'{ODOO_URL}/xmlrpc/2/object')
            uid = common.authenticate(ODOO_DB, ODOO_USERNAME, ODOO_PASSWORD, {})

            if not uid:
                self.send_error_response("Authentication failed")
                return

            # Handle different API endpoints
            if self.path == '/api/connect':
                self.handle_connect(models, uid)
            elif self.path == '/api/attendance/person-types':
                self.handle_person_types(models, uid)
            elif self.path == '/api/attendance/locations':
                self.handle_locations(models, uid)
            elif self.path == '/api/attendance/persons':
                self.handle_persons(models, uid)
            elif self.path == '/api/attendance/check-in':
                self.handle_check_in(models, uid)
            else:
                self.send_error_response("Endpoint not found")

        except Exception as e:
            self.send_error_response(str(e))

    def handle_connect(self, models, uid):
        """Handle connection request from frontend"""
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))

            # Since we already authenticated to get here, just return success
            # Return empty string for URL so frontend uses relative paths
            self.send_json_response({
                'success': True,
                'uid': uid,
                'session_id': 'proxy_session',
                'url': '',  # Use relative paths
                'message': 'Connected via proxy server'
            })

        except Exception as e:
            self.send_error_response(str(e))

    def handle_person_types(self, models, uid):
        try:
            person_types = models.execute_kw(
                ODOO_DB, uid, ODOO_PASSWORD,
                'person.type', 'search_read',
                [[]], {'fields': ['name', 'code', 'description', 'default_access_level', 'active', 'is_system']}
            )
            
            data = []
            for pt in person_types:
                # Count persons of this type
                person_count = models.execute_kw(
                    ODOO_DB, uid, ODOO_PASSWORD,
                    'extended.attendance.person', 'search_count',
                    [[('person_type_id', '=', pt['id'])]]
                )
                
                data.append({
                    'id': pt['id'],
                    'name': pt['name'],
                    'code': pt['code'],
                    'description': pt['description'] or '',
                    'access_level': pt['default_access_level'],
                    'default_access_level': pt['default_access_level'],
                    'active': pt['active'],
                    'is_system': pt['is_system'],
                    'person_count': person_count
                })
            
            self.send_json_response({'success': True, 'data': data})
            
        except Exception as e:
            self.send_error_response(str(e))
    
    def handle_locations(self, models, uid):
        try:
            locations = models.execute_kw(
                ODOO_DB, uid, ODOO_PASSWORD,
                'attendance.location', 'search_read',
                [[('active', '=', True)]], 
                {'fields': ['name', 'code', 'capacity', 'current_occupancy', 'building', 'floor', 'active']}
            )
            
            data = []
            for loc in locations:
                data.append({
                    'id': loc['id'],
                    'name': loc['name'],
                    'code': loc['code'],
                    'capacity': loc['capacity'],
                    'current_occupancy': loc['current_occupancy'],
                    'building': loc['building'] or '',
                    'floor': loc['floor'] or '',
                    'active': loc['active']
                })
            
            self.send_json_response({'success': True, 'data': data})
            
        except Exception as e:
            self.send_error_response(str(e))
    
    def handle_persons(self, models, uid):
        try:
            persons = models.execute_kw(
                ODOO_DB, uid, ODOO_PASSWORD,
                'extended.attendance.person', 'search_read',
                [[]], 
                {'fields': ['name', 'person_id', 'person_type_id', 'email', 'phone', 'active']}
            )
            
            data = []
            for person in persons:
                # Get person type info
                person_type = {'name': '', 'code': ''}
                if person['person_type_id']:
                    pt_info = models.execute_kw(
                        ODOO_DB, uid, ODOO_PASSWORD,
                        'person.type', 'read',
                        [person['person_type_id'][0]], {'fields': ['name', 'code']}
                    )
                    if pt_info:
                        person_type = {'name': pt_info[0]['name'], 'code': pt_info[0]['code']}
                
                # Check current attendance status
                current_attendance = models.execute_kw(
                    ODOO_DB, uid, ODOO_PASSWORD,
                    'extended.attendance.record', 'search_read',
                    [[('person_id', '=', person['id']), ('state', '=', 'checked_in')]],
                    {'fields': ['location_id'], 'limit': 1}
                )
                
                is_checked_in = bool(current_attendance)
                current_location = None
                if current_attendance and current_attendance[0]['location_id']:
                    loc_info = models.execute_kw(
                        ODOO_DB, uid, ODOO_PASSWORD,
                        'attendance.location', 'read',
                        [current_attendance[0]['location_id'][0]], {'fields': ['name', 'code']}
                    )
                    if loc_info:
                        current_location = {'name': loc_info[0]['name'], 'code': loc_info[0]['code']}
                
                data.append({
                    'id': person['id'],
                    'name': person['name'],
                    'person_id': person['person_id'],
                    'person_type': person_type,
                    'is_checked_in': is_checked_in,
                    'current_location': current_location,
                    'email': person['email'] or '',
                    'phone': person['phone'] or '',
                    'active': person['active']
                })
            
            self.send_json_response({'success': True, 'data': data})
            
        except Exception as e:
            self.send_error_response(str(e))
    
    def handle_check_in(self, models, uid):
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            person_identifier = data.get('person_identifier')
            location_code = data.get('location_code')
            
            if not person_identifier or not location_code:
                self.send_error_response('person_identifier and location_code are required')
                return
            
            # Find person
            person_ids = models.execute_kw(
                ODOO_DB, uid, ODOO_PASSWORD,
                'extended.attendance.person', 'search',
                [[('person_id', '=', person_identifier)]]
            )
            
            if not person_ids:
                self.send_error_response(f'Person not found with identifier: {person_identifier}')
                return
            
            # Find location
            location_ids = models.execute_kw(
                ODOO_DB, uid, ODOO_PASSWORD,
                'attendance.location', 'search',
                [[('code', '=', location_code)]]
            )
            
            if not location_ids:
                self.send_error_response(f'Location not found with code: {location_code}')
                return
            
            # Create attendance record using the person's method
            person_id = person_ids[0]
            location_id = location_ids[0]
            
            attendance_id = models.execute_kw(
                ODOO_DB, uid, ODOO_PASSWORD,
                'extended.attendance.person', 'create_attendance',
                [person_id], {'location_id': location_id, 'action': 'check_in'}
            )
            
            # Get the created attendance record
            attendance = models.execute_kw(
                ODOO_DB, uid, ODOO_PASSWORD,
                'extended.attendance.record', 'read',
                [attendance_id], {'fields': ['person_name', 'location_name', 'check_in', 'state']}
            )[0]
            
            self.send_json_response({
                'success': True,
                'message': f'Successfully checked in {attendance["person_name"]}',
                'data': {
                    'id': attendance_id,
                    'person_name': attendance['person_name'],
                    'location_name': attendance['location_name'],
                    'check_in': attendance['check_in'],
                    'state': attendance['state']
                }
            })
            
        except Exception as e:
            self.send_error_response(str(e))
    
    def send_json_response(self, data):
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        self.wfile.write(json.dumps(data).encode())
    
    def send_error_response(self, error_message):
        self.send_response(400)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps({'success': False, 'error': error_message}).encode())
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

if __name__ == "__main__":
    print(f"🚀 Starting Frontend Server on port {PORT}")
    print(f"📁 Serving files from: new_frontend_v2/")
    print(f"🔗 Proxying API calls to: {ODOO_URL}")
    print(f"🌐 Access the frontend at: http://localhost:{PORT}")
    
    with socketserver.TCPServer(("", PORT), OdooAPIHandler) as httpd:
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Server stopped")
