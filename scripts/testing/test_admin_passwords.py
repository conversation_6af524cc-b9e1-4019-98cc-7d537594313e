#!/usr/bin/env python3
"""
Test admin <NAME_EMAIL>
"""

import xmlrpc.client

# Odoo connection settings
ODOO_URL = 'http://localhost:10017'
ODOO_DB = 'extended_attendance'

def test_admin_passwords():
    """Test various <NAME_EMAIL>"""
    try:
        common = xmlrpc.client.ServerProxy(f'{ODOO_URL}/xmlrpc/2/common')
        
        # Try common admin passwords
        admin_passwords = [
            'admin', 'password', '123456', 'odoo', '', 'admin123', 
            'demo', 'Administrator', 'mitchell', 'Mitchell', 
            '<EMAIL>', 'demo123', 'password123', 'admin@demo',
            'test', 'Test', '1234', '12345', 'qwerty'
        ]
        
        print(f"🔍 Testing <NAME_EMAIL>...")
        for password in admin_passwords:
            try:
                admin_uid = common.authenticate(ODOO_DB, '<EMAIL>', password, {})
                if admin_uid:
                    print(f"✅ SUCCESS! Password: '{password}' (User ID: {admin_uid})")
                    return '<EMAIL>', password
                else:
                    print(f"❌ Failed: <EMAIL> / {password}")
            except Exception as e:
                print(f"❌ <NAME_EMAIL>/{password}: {e}")
                
        print("❌ No working password found")
        return None, None
                
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return None, None

if __name__ == "__main__":
    test_admin_passwords()
