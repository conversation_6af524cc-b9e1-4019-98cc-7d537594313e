# Example Scripts

Example scripts demonstrating how to use the Extended Attendance system.

## Scripts

### `extended_attendance_example.py`
Comprehensive example showing:
- Person management
- Location setup
- Attendance tracking
- API usage

### `simple_extended_attendance.py`
Simple example covering:
- Basic attendance operations
- Person creation
- Check-in/check-out

### `quick_start.py`
Quick demonstration of:
- Core features
- Basic setup
- Common operations

## Usage

Run any example:
```bash
python3 scripts/examples/extended_attendance_example.py
python3 scripts/examples/simple_extended_attendance.py
python3 scripts/examples/quick_start.py
```

These scripts are safe to run and will demonstrate the system without affecting production data.
