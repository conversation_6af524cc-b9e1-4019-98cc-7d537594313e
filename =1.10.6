Collecting fastapi
  Downloading fastapi-0.116.1-py3-none-any.whl.metadata (28 kB)
Collecting python-multipart
  Downloading python_multipart-0.0.20-py3-none-any.whl.metadata (1.8 kB)
Collecting ujson
  Downloading ujson-5.10.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (9.3 kB)
Collecting a2wsgi
  Downloading a2wsgi-1.10.10-py3-none-any.whl.metadata (4.0 kB)
Collecting parse-accept-language
  Downloading parse_accept_language-0.1.2-py2.py3-none-any.whl.metadata (882 bytes)
Collecting starlette<0.48.0,>=0.40.0 (from fastapi)
  Downloading starlette-0.47.1-py3-none-any.whl.metadata (6.2 kB)
Collecting pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4 (from fastapi)
  Downloading pydantic-2.11.7-py3-none-any.whl.metadata (67 kB)
Collecting typing-extensions>=4.8.0 (from fastapi)
  Downloading typing_extensions-4.14.1-py3-none-any.whl.metadata (3.0 kB)
Collecting annotated-types>=0.6.0 (from pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4->fastapi)
  Downloading annotated_types-0.7.0-py3-none-any.whl.metadata (15 kB)
Collecting pydantic-core==2.33.2 (from pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4->fastapi)
  Downloading pydantic_core-2.33.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.8 kB)
Collecting typing-inspection>=0.4.0 (from pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4->fastapi)
  Downloading typing_inspection-0.4.1-py3-none-any.whl.metadata (2.6 kB)
Collecting anyio<5,>=3.6.2 (from starlette<0.48.0,>=0.40.0->fastapi)
  Downloading anyio-4.9.0-py3-none-any.whl.metadata (4.7 kB)
Collecting exceptiongroup>=1.0.2 (from anyio<5,>=3.6.2->starlette<0.48.0,>=0.40.0->fastapi)
  Downloading exceptiongroup-1.3.0-py3-none-any.whl.metadata (6.7 kB)
Requirement already satisfied: idna>=2.8 in /usr/lib/python3/dist-packages (from anyio<5,>=3.6.2->starlette<0.48.0,>=0.40.0->fastapi) (3.3)
Collecting sniffio>=1.1 (from anyio<5,>=3.6.2->starlette<0.48.0,>=0.40.0->fastapi)
  Downloading sniffio-1.3.1-py3-none-any.whl.metadata (3.9 kB)
Downloading fastapi-0.116.1-py3-none-any.whl (95 kB)
Downloading pydantic-2.11.7-py3-none-any.whl (444 kB)
Downloading pydantic_core-2.33.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.0 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 2.0/2.0 MB 4.0 MB/s eta 0:00:00
Downloading starlette-0.47.1-py3-none-any.whl (72 kB)
Downloading anyio-4.9.0-py3-none-any.whl (100 kB)
Downloading python_multipart-0.0.20-py3-none-any.whl (24 kB)
Downloading ujson-5.10.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (53 kB)
Downloading a2wsgi-1.10.10-py3-none-any.whl (17 kB)
Downloading parse_accept_language-0.1.2-py2.py3-none-any.whl (3.6 kB)
Downloading annotated_types-0.7.0-py3-none-any.whl (13 kB)
Downloading exceptiongroup-1.3.0-py3-none-any.whl (16 kB)
Downloading sniffio-1.3.1-py3-none-any.whl (10 kB)
Downloading typing_extensions-4.14.1-py3-none-any.whl (43 kB)
Downloading typing_inspection-0.4.1-py3-none-any.whl (14 kB)
Installing collected packages: parse-accept-language, ujson, typing-extensions, sniffio, python-multipart, annotated-types, typing-inspection, pydantic-core, exceptiongroup, a2wsgi, pydantic, anyio, starlette, fastapi

Successfully installed a2wsgi-1.10.10 annotated-types-0.7.0 anyio-4.9.0 exceptiongroup-1.3.0 fastapi-0.116.1 parse-accept-language-0.1.2 pydantic-2.11.7 pydantic-core-2.33.2 python-multipart-0.0.20 sniffio-1.3.1 starlette-0.47.1 typing-extensions-4.14.1 typing-inspection-0.4.1 ujson-5.10.0
