name: Mark stale issues and pull requests

on:
  schedule:
    - cron: "0 12 * * 0"

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - name: Stale PRs and issues policy
        uses: actions/stale@v9
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          # General settings.
          ascending: true
          remove-stale-when-updated: true
          # Pull Requests settings.
          # 120+30 day stale policy for PRs
          # * Except PRs marked as "no stale"
          days-before-pr-stale: 120
          days-before-pr-close: 30
          exempt-pr-labels: "no stale"
          stale-pr-label: "stale"
          stale-pr-message: >
            There hasn't been any activity on this pull request in the past 4 months, so
            it has been marked as stale and it will be closed automatically if no
            further activity occurs in the next 30 days.

            If you want this PR to never become stale, please ask a PSC member to apply
            the "no stale" label.
          # Issues settings.
          # 180+30 day stale policy for open issues
          # * Except Issues marked as "no stale"
          days-before-issue-stale: 180
          days-before-issue-close: 30
          exempt-issue-labels: "no stale,needs more information"
          stale-issue-label: "stale"
          stale-issue-message: >
            There hasn't been any activity on this issue in the past 6 months, so it has
            been marked as stale and it will be closed automatically if no further
            activity occurs in the next 30 days.

            If you want this issue to never become stale, please ask a PSC member to
            apply the "no stale" label.

      # 15+30 day stale policy for issues pending more information
      # * Issues that are pending more information
      # * Except Issues marked as "no stale"
      - name: Needs more information stale issues policy
        uses: actions/stale@v9
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          ascending: true
          only-labels: "needs more information"
          exempt-issue-labels: "no stale"
          days-before-stale: 15
          days-before-close: 30
          days-before-pr-stale: -1
          days-before-pr-close: -1
          remove-stale-when-updated: true
          stale-issue-label: "stale"
          stale-issue-message: >
            This issue needs more information and there hasn't been any activity
            recently, so it has been marked as stale and it will be closed automatically
            if no further activity occurs in the next 30 days.

            If you think this is a mistake, please ask a PSC member to remove the "needs
            more information" label.
