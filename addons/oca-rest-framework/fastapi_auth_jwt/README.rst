========================
FastAPI Auth JWT support
========================

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:2829a34d48a1906819029e7b796d33a1ee2ad2a47693396da96f92ede04ec17d
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Beta-yellow.png
    :target: https://odoo-community.org/page/development-status
    :alt: Beta
.. |badge2| image:: https://img.shields.io/badge/licence-LGPL--3-blue.png
    :target: http://www.gnu.org/licenses/lgpl-3.0-standalone.html
    :alt: License: LGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Frest--framework-lightgray.png?logo=github
    :target: https://github.com/OCA/rest-framework/tree/16.0/fastapi_auth_jwt
    :alt: OCA/rest-framework
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/rest-framework-16-0/rest-framework-16-0-fastapi_auth_jwt
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/rest-framework&target_branch=16.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This module provides ``FastAPI`` ``Depends`` to allow authentication with `auth_jwt
<https://github.com/OCA/server-auth/tree/16.0/auth_jwt>`_.

**Table of contents**

.. contents::
   :local:

Usage
=====

The following FastAPI dependencies  are provided and importable from
``odoo.addons.fastapi_auth_jwt.dependencies``:

``def auth_jwt_authenticated_payload() -> Payload``

  Return the authenticated JWT payload. Raise a 401 (unauthorized) if absent or invalid.

``def auth_jwt_optionally_authenticated_payload() -> Payload | None``

  Return the authenticated JWT payload, or ``None`` if the ``Authorization`` header and
  cookie are absent. Raise a 401 (unauthorized) if present and invalid.

``def auth_jwt_authenticated_partner() -> Partner``

  Obtain the authenticated partner corresponding to the provided JWT token, according to
  the partner strategy defined on the ``auth_jwt`` validator. Raise a 401 (unauthorized)
  if the partner could not be determined for any reason.

  This is function suitable and intended to override
  ``odoo.addons.fastapi.dependencies.authenticated_partner_impl``.

  The partner record returned by this function is bound to an environment that uses the
  Odoo user obtained from the user strategy defined on the ``auth_jwt`` validator. When
  used ``authenticated_partner_impl`` this in turn ensures that
  ``odoo.addons.fastapi.dependencies.authenticated_partner_env`` is also bound to the
  correct Odoo user.

``def auth_jwt_optionally_authenticated_partner() -> Partner``

  Same as ``auth_jwt_partner`` except it returns an empty recordset bound to the
  ``public`` user if the ``Authorization`` header and cookie are absent, or if the JWT
  validator could not find the partner and declares that the partner is not required.

``def auth_jwt_authenticated_odoo_env() -> Environment``

  Return an Odoo environment using the the Odoo user obtained from the user strategy
  defined on the ``auth_jwt`` validator, if the request could be authenticated using a
  JWT validator. Raise a 401 (unauthorized) otherwise.

  This is function suitable and intended to override
  ``odoo.addons.fastapi.dependencies.authenticated_odoo_env_impl``.

``def auth_jwt_default_validator_name() -> str | None``

  Return the name of the default JWT validator to use.

  The default implementation returns ``None`` meaning only one active JWT validator is
  allowed. This dependency is meant to be overridden.

``def auth_jwt_http_header_authorization() -> str | None``

  By default, return the credentials part of the ``Authorization`` header, or ``None``
  if absent. This dependency is meant to be overridden, in particular with
  ``fastapi.security.OAuth2AuthorizationCodeBearer`` to let swagger handle OAuth2
  authorization (such override is only necessary for comfort when using the swagger
  interface).

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/rest-framework/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/rest-framework/issues/new?body=module:%20fastapi_auth_jwt%0Aversion:%2016.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
~~~~~~~

* ACSONE SA/NV

Maintainers
~~~~~~~~~~~

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

.. |maintainer-sbidoul| image:: https://github.com/sbidoul.png?size=40px
    :target: https://github.com/sbidoul
    :alt: sbidoul

Current `maintainer <https://odoo-community.org/page/maintainer-role>`__:

|maintainer-sbidoul| 

This module is part of the `OCA/rest-framework <https://github.com/OCA/rest-framework/tree/16.0/fastapi_auth_jwt>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
