=========
Datamodel
=========

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:5411d4f742eb933a4d05f5f6e1784a7ddc042e7f22b1c08d535e225c306a6955
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Beta-yellow.png
    :target: https://odoo-community.org/page/development-status
    :alt: Beta
.. |badge2| image:: https://img.shields.io/badge/licence-LGPL--3-blue.png
    :target: http://www.gnu.org/licenses/lgpl-3.0-standalone.html
    :alt: License: LGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Frest--framework-lightgray.png?logo=github
    :target: https://github.com/OCA/rest-framework/tree/16.0/datamodel
    :alt: OCA/rest-framework
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/rest-framework-16-0/rest-framework-16-0-datamodel
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/rest-framework&target_branch=16.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This addon allows you to define simple data models supporting serialization/deserialization
to/from json

Datamodels are `Marshmallow models <https://github.com/sv-tools/marshmallow-objects>`_ classes that can be inherited as Odoo
Models.

**Table of contents**

.. contents::
   :local:

Usage
=====

To define your own datamodel you just need to create a class that inherits from
``odoo.addons.datamodel.core.Datamodel``

.. code-block:: python

    from marshmallow import fields

    from odoo.addons.base_rest import restapi
    from odoo.addons.component.core import Component
    from odoo.addons.datamodel.core import Datamodel


    class PartnerShortInfo(Datamodel):
        _name = "partner.short.info"

        id = fields.Integer(required=True, allow_none=False)
        name = fields.String(required=True, allow_none=False)

    class PartnerInfo(Datamodel):
        _name = "partner.info"
        _inherit = "partner.short.info"

        street = fields.String(required=True, allow_none=False)
        street2 = fields.String(required=False, allow_none=True)
        zip_code = fields.String(required=True, allow_none=False)
        city = fields.String(required=True, allow_none=False)
        phone = fields.String(required=False, allow_none=True)
        is_componay = fields.Boolean(required=False, allow_none=False)


As for odoo models, you can extend the `base` datamodel by inheriting of `base`.

.. code-block:: python

    class Base(Datamodel):
        _inherit = "base"

        def _my_method(self):
            pass

Datamodels are available through the `datamodels` registry provided by the Odoo's environment.

.. code-block:: python

    class ResPartner(Model):
        _inherit = "res.partner"

        def _to_partner_info(self):
            PartnerInfo = self.env.datamodels["partner.info"]
            partner_info = PartnerInfo(partial=True)
            partner_info.id = partner.id
            partner_info.name = partner.name
            partner_info.street = partner.street
            partner_info.street2 = partner.street2
            partner_info.zip_code = partner.zip
            partner_info.city = partner.city
            partner_info.phone = partner.phone
            partner_info.is_company = partner.is_company
            return partner_info

The Odoo's environment is also available into the datamodel instance.

.. code-block:: python

    class MyDataModel(Datamodel):
        _name = "my.data.model"

        def _my_method(self):
            partners = self.env["res.partner"].search([])

.. warning::

  The `env` property into a Datamodel instance is mutable. IOW, you can't rely
  on information (context, user) provided by the environment. The `env` property
  is a helper property that give you access to the odoo's registry and must
  be use with caution.

Known issues / Roadmap
======================

The `roadmap <https://github.com/OCA/rest-framework/issues?q=is%3Aopen+is%3Aissue+label%3Aenhancement+label%3Adatamodel>`_
and `known issues <https://github.com/OCA/rest-framework/issues?q=is%3Aopen+is%3Aissue+label%3Abug+label%3Adatamodel>`_ can
be found on GitHub.

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/rest-framework/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/rest-framework/issues/new?body=module:%20datamodel%0Aversion:%2016.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
~~~~~~~

* ACSONE SA/NV

Contributors
~~~~~~~~~~~~

* Laurent Mignon <<EMAIL>>
* `Tecnativa <https://www.tecnativa.com>`_:

    * Carlos Roca

Maintainers
~~~~~~~~~~~

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

.. |maintainer-lmignon| image:: https://github.com/lmignon.png?size=40px
    :target: https://github.com/lmignon
    :alt: lmignon

Current `maintainer <https://odoo-community.org/page/maintainer-role>`__:

|maintainer-lmignon| 

This module is part of the `OCA/rest-framework <https://github.com/OCA/rest-framework/tree/16.0/datamodel>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
