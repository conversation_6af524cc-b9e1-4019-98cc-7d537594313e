========
REST Log
========

.. !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Beta-yellow.png
    :target: https://odoo-community.org/page/development-status
    :alt: Beta
.. |badge2| image:: https://img.shields.io/badge/licence-LGPL--3-blue.png
    :target: http://www.gnu.org/licenses/lgpl-3.0-standalone.html
    :alt: License: LGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Frest--framework-lightgray.png?logo=github
    :target: https://github.com/OCA/rest-framework/tree/15.0/rest_log
    :alt: OCA/rest-framework
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/rest-framework-15-0/rest-framework-15-0-rest_log
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runbot-Try%20me-875A7B.png
    :target: https://runbot.odoo-community.org/runbot/271/15.0
    :alt: Try me on Runbot

|badge1| |badge2| |badge3| |badge4| |badge5| 

When exposing REST services is often useful to see what's happening
especially in case of errors.

This module add DB logging for REST requests.
It also inject in the response the URL of the log entry created.

NOTE: this feature was implemented initially inside shopfloor app.
Up to version 13.0.1.2.1 of this module,
if shopfloor is installed, log records will be copied from its table.

**Table of contents**

.. contents::
   :local:

Configuration
=============

Logs retention
~~~~~~~~~~~~~~

Logs are kept in database for every REST requests made by a client application.
They can be used for debugging and monitoring of the activity.

The Logs menu is shown only with Developer tools (``?debug=1``) activated.

By default, REST logs are kept 30 days.
You can change the duration of the retention by changing the System Parameter
``rest.log.retention.days``.

If the value is set to 0, the logs are not stored at all.

Logged data is: request URL and method, parameters, headers, result or error.


Logs activation
~~~~~~~~~~~~~~~

You have 2 ways to activate logging:

* on the service component set `_log_calls_in_db = True`
* via configuration

In the 1st case, calls will be always be logged.

In the 2nd case you can set ``rest.log.active`` param as::

    `collection_name`  # enable for all endpoints of the collection
    `collection_name.usage`  # enable for specific endpoints
    `collection_name.usage.endpoint`  # enable for specific endpoints
    `collection_name*:state`  # enable only for specific state (success, failed)

Changelog
=========

13.0.1.0.0
~~~~~~~~~~

First official version.

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/rest-framework/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us smashing it by providing a detailed and welcomed
`feedback <https://github.com/OCA/rest-framework/issues/new?body=module:%20rest_log%0Aversion:%2015.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
~~~~~~~

* Camptocamp
* ACSONE

Contributors
~~~~~~~~~~~~

* Guewen Baconnier <<EMAIL>>
* Simone Orsi <<EMAIL>>

Other credits
~~~~~~~~~~~~~

**Financial support**

* Cosanum
* Camptocamp R&D
* ACSONE R&D

Maintainers
~~~~~~~~~~~

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

.. |maintainer-simahawk| image:: https://github.com/simahawk.png?size=40px
    :target: https://github.com/simahawk
    :alt: simahawk

Current `maintainer <https://odoo-community.org/page/maintainer-role>`__:

|maintainer-simahawk| 

This module is part of the `OCA/rest-framework <https://github.com/OCA/rest-framework/tree/15.0/rest_log>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
