<?xml version="1.0" encoding="utf-8" ?>
<odoo noupdate="1">
    <record id="ir_cron_autovacuum_rest_log" model="ir.cron">
        <field name="name">Auto-vacuum REST Logs</field>
        <field ref="model_rest_log" name="model_id" />
        <field eval="True" name="active" />
        <field name="user_id" ref="base.user_root" />
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field eval="False" name="doall" />
        <field name="state">code</field>
        <field name="code">model.autovacuum()</field>
    </record>
</odoo>
