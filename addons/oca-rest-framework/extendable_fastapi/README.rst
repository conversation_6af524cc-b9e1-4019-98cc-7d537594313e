==================
Extendable Fastapi
==================

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:4e4f5d96294f860ce7f0c4e023431f8ed9ca011c318b5ba4a3cfcd15c31eac1a
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Beta-yellow.png
    :target: https://odoo-community.org/page/development-status
    :alt: Beta
.. |badge2| image:: https://img.shields.io/badge/licence-LGPL--3-blue.png
    :target: http://www.gnu.org/licenses/lgpl-3.0-standalone.html
    :alt: License: LGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Frest--framework-lightgray.png?logo=github
    :target: https://github.com/OCA/rest-framework/tree/16.0/extendable_fastapi
    :alt: OCA/rest-framework
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/rest-framework-16-0/rest-framework-16-0-extendable_fastapi
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/rest-framework&target_branch=16.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This addon is a technical addon used to allows the use of
`extendable <https://pypi.org/project/extendable/>`_
classes in the implementation of your fastapi endpoint handlers. It also
allows you to use `extendable_pydantic <https://pypi.org/project/extendable_pydantic/>`_
models when defining your endpoint handlers request and response models.

**Table of contents**

.. contents::
   :local:

Changelog
=========

16.0.2.1.1 (2023-11-07)
~~~~~~~~~~~~~~~~~~~~~~~

**Bugfixes**

- Fix registry corruption when running tests difined in a class inheriting of the *FastAPITransactionCase* class if an error occurs in the *setUpClass* after the call to super(). (`#392 <https://github.com/OCA/rest-framework/issues/392>`_)


16.0.2.1.0 (2023-10-13)
~~~~~~~~~~~~~~~~~~~~~~~

**Features**

- * New base schemas: *PagedCollection*. This schema is used to define the
    the structure of a paged collection of resources. This schema is similar
    to the ones defined in the Odoo's **fastapi** addon but works as/with
    extendable models.

  * The *StrictExtendableBaseModel* has been moved to the *extendable_pydantic*
    python lib. You should consider to import it from there. (`#380 <https://github.com/OCA/rest-framework/issues/380>`_)

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/rest-framework/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/rest-framework/issues/new?body=module:%20extendable_fastapi%0Aversion:%2016.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
~~~~~~~

* ACSONE SA/NV

Contributors
~~~~~~~~~~~~

* Laurent Mignon <<EMAIL>> (https://acsone.eu)
* Marie Lejeune <<EMAIL>> (https://acsone.eu)

Maintainers
~~~~~~~~~~~

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

.. |maintainer-lmignon| image:: https://github.com/lmignon.png?size=40px
    :target: https://github.com/lmignon
    :alt: lmignon

Current `maintainer <https://odoo-community.org/page/maintainer-role>`__:

|maintainer-lmignon| 

This module is part of the `OCA/rest-framework <https://github.com/OCA/rest-framework/tree/16.0/extendable_fastapi>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
