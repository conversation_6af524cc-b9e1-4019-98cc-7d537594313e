# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* model_serializer
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: model_serializer
#: code:addons/model_serializer/core.py:0
#, python-format
msgid "'{}' datamodel does not exist"
msgstr ""

#. module: model_serializer
#: model:ir.model,name:model_serializer.model_datamodel_builder
msgid "Datamodel Builder"
msgstr ""

#. module: model_serializer
#: code:addons/model_serializer/core.py:0
#, python-format
msgid ""
"Error in {}: Model Serializers cannot inherit from a class having a "
"different '_model' attribute"
msgstr ""

#. module: model_serializer
#: code:addons/model_serializer/core.py:0
#, python-format
msgid ""
"Error in {}: Model Serializers require '_model' and '_model_fields' "
"attributes to be defined"
msgstr ""
