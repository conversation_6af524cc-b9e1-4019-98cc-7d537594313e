<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="generator" content="Docutils 0.15.1: http://docutils.sourceforge.net/" />
<title>Model Serializer</title>
<style type="text/css">

/*
:Author: <PERSON> (<EMAIL>)
:Id: $Id: html4css1.css 7952 2016-07-26 18:15:59Z milde $
:Copyright: This stylesheet has been placed in the public domain.

Default cascading style sheet for the HTML output of Docutils.

See http://docutils.sf.net/docs/howto/html-stylesheets.html for how to
customize this style sheet.
*/

/* used to remove borders from tables and images */
.borderless, table.borderless td, table.borderless th {
  border: 0 }

table.borderless td, table.borderless th {
  /* Override padding for "table.docutils td" with "! important".
     The right padding separates the table cells. */
  padding: 0 0.5em 0 0 ! important }

.first {
  /* Override more specific margin styles with "! important". */
  margin-top: 0 ! important }

.last, .with-subtitle {
  margin-bottom: 0 ! important }

.hidden {
  display: none }

.subscript {
  vertical-align: sub;
  font-size: smaller }

.superscript {
  vertical-align: super;
  font-size: smaller }

a.toc-backref {
  text-decoration: none ;
  color: black }

blockquote.epigraph {
  margin: 2em 5em ; }

dl.docutils dd {
  margin-bottom: 0.5em }

object[type="image/svg+xml"], object[type="application/x-shockwave-flash"] {
  overflow: hidden;
}

/* Uncomment (and remove this text!) to get bold-faced definition list terms
dl.docutils dt {
  font-weight: bold }
*/

div.abstract {
  margin: 2em 5em }

div.abstract p.topic-title {
  font-weight: bold ;
  text-align: center }

div.admonition, div.attention, div.caution, div.danger, div.error,
div.hint, div.important, div.note, div.tip, div.warning {
  margin: 2em ;
  border: medium outset ;
  padding: 1em }

div.admonition p.admonition-title, div.hint p.admonition-title,
div.important p.admonition-title, div.note p.admonition-title,
div.tip p.admonition-title {
  font-weight: bold ;
  font-family: sans-serif }

div.attention p.admonition-title, div.caution p.admonition-title,
div.danger p.admonition-title, div.error p.admonition-title,
div.warning p.admonition-title, .code .error {
  color: red ;
  font-weight: bold ;
  font-family: sans-serif }

/* Uncomment (and remove this text!) to get reduced vertical space in
   compound paragraphs.
div.compound .compound-first, div.compound .compound-middle {
  margin-bottom: 0.5em }

div.compound .compound-last, div.compound .compound-middle {
  margin-top: 0.5em }
*/

div.dedication {
  margin: 2em 5em ;
  text-align: center ;
  font-style: italic }

div.dedication p.topic-title {
  font-weight: bold ;
  font-style: normal }

div.figure {
  margin-left: 2em ;
  margin-right: 2em }

div.footer, div.header {
  clear: both;
  font-size: smaller }

div.line-block {
  display: block ;
  margin-top: 1em ;
  margin-bottom: 1em }

div.line-block div.line-block {
  margin-top: 0 ;
  margin-bottom: 0 ;
  margin-left: 1.5em }

div.sidebar {
  margin: 0 0 0.5em 1em ;
  border: medium outset ;
  padding: 1em ;
  background-color: #ffffee ;
  width: 40% ;
  float: right ;
  clear: right }

div.sidebar p.rubric {
  font-family: sans-serif ;
  font-size: medium }

div.system-messages {
  margin: 5em }

div.system-messages h1 {
  color: red }

div.system-message {
  border: medium outset ;
  padding: 1em }

div.system-message p.system-message-title {
  color: red ;
  font-weight: bold }

div.topic {
  margin: 2em }

h1.section-subtitle, h2.section-subtitle, h3.section-subtitle,
h4.section-subtitle, h5.section-subtitle, h6.section-subtitle {
  margin-top: 0.4em }

h1.title {
  text-align: center }

h2.subtitle {
  text-align: center }

hr.docutils {
  width: 75% }

img.align-left, .figure.align-left, object.align-left, table.align-left {
  clear: left ;
  float: left ;
  margin-right: 1em }

img.align-right, .figure.align-right, object.align-right, table.align-right {
  clear: right ;
  float: right ;
  margin-left: 1em }

img.align-center, .figure.align-center, object.align-center {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

table.align-center {
  margin-left: auto;
  margin-right: auto;
}

.align-left {
  text-align: left }

.align-center {
  clear: both ;
  text-align: center }

.align-right {
  text-align: right }

/* reset inner alignment in figures */
div.align-right {
  text-align: inherit }

/* div.align-center * { */
/*   text-align: left } */

.align-top    {
  vertical-align: top }

.align-middle {
  vertical-align: middle }

.align-bottom {
  vertical-align: bottom }

ol.simple, ul.simple {
  margin-bottom: 1em }

ol.arabic {
  list-style: decimal }

ol.loweralpha {
  list-style: lower-alpha }

ol.upperalpha {
  list-style: upper-alpha }

ol.lowerroman {
  list-style: lower-roman }

ol.upperroman {
  list-style: upper-roman }

p.attribution {
  text-align: right ;
  margin-left: 50% }

p.caption {
  font-style: italic }

p.credits {
  font-style: italic ;
  font-size: smaller }

p.label {
  white-space: nowrap }

p.rubric {
  font-weight: bold ;
  font-size: larger ;
  color: maroon ;
  text-align: center }

p.sidebar-title {
  font-family: sans-serif ;
  font-weight: bold ;
  font-size: larger }

p.sidebar-subtitle {
  font-family: sans-serif ;
  font-weight: bold }

p.topic-title {
  font-weight: bold }

pre.address {
  margin-bottom: 0 ;
  margin-top: 0 ;
  font: inherit }

pre.literal-block, pre.doctest-block, pre.math, pre.code {
  margin-left: 2em ;
  margin-right: 2em }

pre.code .ln { color: grey; } /* line numbers */
pre.code, code { background-color: #eeeeee }
pre.code .comment, code .comment { color: #5C6576 }
pre.code .keyword, code .keyword { color: #3B0D06; font-weight: bold }
pre.code .literal.string, code .literal.string { color: #0C5404 }
pre.code .name.builtin, code .name.builtin { color: #352B84 }
pre.code .deleted, code .deleted { background-color: #DEB0A1}
pre.code .inserted, code .inserted { background-color: #A3D289}

span.classifier {
  font-family: sans-serif ;
  font-style: oblique }

span.classifier-delimiter {
  font-family: sans-serif ;
  font-weight: bold }

span.interpreted {
  font-family: sans-serif }

span.option {
  white-space: nowrap }

span.pre {
  white-space: pre }

span.problematic {
  color: red }

span.section-subtitle {
  /* font-size relative to parent (h1..h6 element) */
  font-size: 80% }

table.citation {
  border-left: solid 1px gray;
  margin-left: 1px }

table.docinfo {
  margin: 2em 4em }

table.docutils {
  margin-top: 0.5em ;
  margin-bottom: 0.5em }

table.footnote {
  border-left: solid 1px black;
  margin-left: 1px }

table.docutils td, table.docutils th,
table.docinfo td, table.docinfo th {
  padding-left: 0.5em ;
  padding-right: 0.5em ;
  vertical-align: top }

table.docutils th.field-name, table.docinfo th.docinfo-name {
  font-weight: bold ;
  text-align: left ;
  white-space: nowrap ;
  padding-left: 0 }

/* "booktabs" style (no vertical lines) */
table.docutils.booktabs {
  border: 0px;
  border-top: 2px solid;
  border-bottom: 2px solid;
  border-collapse: collapse;
}
table.docutils.booktabs * {
  border: 0px;
}
table.docutils.booktabs th {
  border-bottom: thin solid;
  text-align: left;
}

h1 tt.docutils, h2 tt.docutils, h3 tt.docutils,
h4 tt.docutils, h5 tt.docutils, h6 tt.docutils {
  font-size: 100% }

ul.auto-toc {
  list-style-type: none }

</style>
</head>
<body>
<div class="document" id="model-serializer">
<h1 class="title">Model Serializer</h1>

<!-- !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
!! This file is generated by oca-gen-addon-readme !!
!! changes will be overwritten.                   !!
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! -->
<p><a class="reference external" href="https://odoo-community.org/page/development-status"><img alt="Alpha" src="https://img.shields.io/badge/maturity-Alpha-red.png" /></a> <a class="reference external" href="http://www.gnu.org/licenses/lgpl-3.0-standalone.html"><img alt="License: LGPL-3" src="https://img.shields.io/badge/licence-LGPL--3-blue.png" /></a> <a class="reference external" href="https://github.com/OCA/rest-framework/tree/15.0/model_serializer"><img alt="OCA/rest-framework" src="https://img.shields.io/badge/github-OCA%2Frest--framework-lightgray.png?logo=github" /></a> <a class="reference external" href="https://translation.odoo-community.org/projects/rest-framework-15-0/rest-framework-15-0-model_serializer"><img alt="Translate me on Weblate" src="https://img.shields.io/badge/weblate-Translate%20me-F47D42.png" /></a> <a class="reference external" href="https://runbot.odoo-community.org/runbot/271/15.0"><img alt="Try me on Runbot" src="https://img.shields.io/badge/runbot-Try%20me-875A7B.png" /></a></p>
<p>This module takes advantage of the concepts introduced in the <cite>datamodel</cite> module to offer mechanisms similar to
(a subset of) the <cite>ModelSerializer</cite> in Django REST Framework. That is, use the definition of the Odoo model to
partially automate the definition of a corresponding <cite>Datamodel</cite> class.</p>
<div class="admonition important">
<p class="first admonition-title">Important</p>
<p class="last">This is an alpha version, the data model and design can change at any time without warning.
Only for development or testing purpose, do not use in production.
<a class="reference external" href="https://odoo-community.org/page/development-status">More details on development status</a></p>
</div>
<p><strong>Table of contents</strong></p>
<div class="contents local topic" id="contents">
<ul class="simple">
<li><a class="reference internal" href="#usage" id="id3">Usage</a><ul>
<li><a class="reference internal" href="#modelserializer-class" id="id4"><code>ModelSerializer</code> class</a><ul>
<li><a class="reference internal" href="#basic-usage" id="id5">Basic usage</a></li>
<li><a class="reference internal" href="#overriding-fields-definition" id="id6">Overriding fields definition</a></li>
<li><a class="reference internal" href="#de-serialization" id="id7">(De)serialization</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#changelog" id="id8">Changelog</a><ul>
<li><a class="reference internal" href="#id1" id="id9">14.0.1.0.0</a></li>
<li><a class="reference internal" href="#id2" id="id10">15.0.1.0.0</a></li>
</ul>
</li>
<li><a class="reference internal" href="#bug-tracker" id="id11">Bug Tracker</a></li>
<li><a class="reference internal" href="#credits" id="id12">Credits</a><ul>
<li><a class="reference internal" href="#authors" id="id13">Authors</a></li>
<li><a class="reference internal" href="#contributors" id="id14">Contributors</a></li>
<li><a class="reference internal" href="#maintainers" id="id15">Maintainers</a></li>
</ul>
</li>
</ul>
</div>
<div class="section" id="usage">
<h1><a class="toc-backref" href="#id3">Usage</a></h1>
<div class="section" id="modelserializer-class">
<h2><a class="toc-backref" href="#id4"><code>ModelSerializer</code> class</a></h2>
<p>The <code>ModelSerializer</code> class inherits from the <code>Datamodel</code> class and adds functionalities. Therefore any
class inheriting from <code>ModelSerializer</code> can be used the exact same way as any other <code>Datamodel</code>.</p>
<div class="section" id="basic-usage">
<h3><a class="toc-backref" href="#id5">Basic usage</a></h3>
<p>Here is a basic example:</p>
<pre class="literal-block">
from odoo.addons.model_serializer.core import ModelSerializer

class PartnerInfo(ModelSerializer):
    _name = &quot;partner.info&quot;
    _model = &quot;res.partner&quot;
    _model_fields = [&quot;id&quot;, &quot;name&quot;, &quot;country_id&quot;]
</pre>
<p>The result is equivalent to the following <code>Datamodel</code> classes:</p>
<pre class="literal-block">
from marshmallow import fields

from odoo.addons.datamodel.core import Datamodel
from odoo.addons.datamodel.fields import NestedModel


class PartnerInfo(Datamodel):
    _name = &quot;partner.info&quot;

    id = fields.Integer(required=True, allow_none=False, dump_only=True)
    name = fields.String(required=True, allow_none=False)
    country = NestedModel(&quot;_auto_nested_serializer.res.country&quot;)


class _AutoNestedSerializerResCountry(Datamodel):
    _name = &quot;_auto_nested_serializer.res.country&quot;

    id = fields.Integer(required=True, allow_none=False, dump_only=True)
    display_name = fields.String(dump_only=True)
</pre>
</div>
<div class="section" id="overriding-fields-definition">
<h3><a class="toc-backref" href="#id6">Overriding fields definition</a></h3>
<p>It is possible to override the default definition of fields as such:</p>
<pre class="literal-block">
from odoo.addons.model_serializer.core import ModelSerializer

class PartnerInfo(ModelSerializer):
    _name = &quot;partner.info&quot;
    _model = &quot;res.partner&quot;
    _model_fields = [&quot;id&quot;, &quot;name&quot;, &quot;country_id&quot;]

    country_id = NestedModel(&quot;country.info&quot;)

class CountryInfo(ModelSerializer):
    _name = &quot;country.info&quot;
    _model = &quot;res.country&quot;
    _model_fields = [&quot;code&quot;, &quot;name&quot;]
</pre>
<p>In this example, we override a <code>NestedModel</code> but it works the same for any other field type.</p>
</div>
<div class="section" id="de-serialization">
<h3><a class="toc-backref" href="#id7">(De)serialization</a></h3>
<p><code>ModelSerializer</code> does all the heavy-lifting of transforming a <code>Datamodel</code> instance into the corresponding
<code>recordset</code>, and vice-versa.</p>
<p>To transform a recordset into a (list of) <code>ModelSerializer</code> instance(s) (serialization), do the following:</p>
<pre class="literal-block">
partner_info = self.env.datamodels[&quot;partner.info&quot;].from_recordset(partner)
</pre>
<p>This will return a single instance; if your recordset contains more than one record, you can get a list of instances
by passing <code>many=True</code> to this method.</p>
<p>To transform a <code>ModelSerializer</code> instance into a recordset (de-serialization), do the following:</p>
<pre class="literal-block">
partner = partner_info.to_recordset()
</pre>
<p>Unless an existing partner can be found (see below), this method <strong>creates a new record</strong> in the database. You can avoid
that by passing <code>create=False</code>, in which case the system will only create them in memory (<code>NewId</code> recordset).</p>
<p>In order to determine if the corresponding Odoo record already exists or if a new one should be created, the system
checks by default if the <code>id</code> field of the instance corresponds to a database record. This default behavior can be
modified like so:</p>
<pre class="literal-block">
class CountryInfo(ModelSerializer):
    _name = &quot;country.info&quot;
    _model = &quot;res.country&quot;
    _model_fields = [&quot;code&quot;, &quot;name&quot;]

    def get_odoo_record(self):
        if self.code:
            return self.env[self._model].search([(&quot;code&quot;, &quot;=&quot;, self.code)])
        return super().get_odoo_record()
</pre>
</div>
</div>
</div>
<div class="section" id="changelog">
<h1><a class="toc-backref" href="#id8">Changelog</a></h1>
<div class="section" id="id1">
<h2><a class="toc-backref" href="#id9">14.0.1.0.0</a></h2>
<p>First official version.</p>
</div>
<div class="section" id="id2">
<h2><a class="toc-backref" href="#id10">15.0.1.0.0</a></h2>
<p>Second version.</p>
</div>
</div>
<div class="section" id="bug-tracker">
<h1><a class="toc-backref" href="#id11">Bug Tracker</a></h1>
<p>Bugs are tracked on <a class="reference external" href="https://github.com/OCA/rest-framework/issues">GitHub Issues</a>.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us smashing it by providing a detailed and welcomed
<a class="reference external" href="https://github.com/OCA/rest-framework/issues/new?body=module:%20model_serializer%0Aversion:%2015.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**">feedback</a>.</p>
<p>Do not contact contributors directly about support or help with technical issues.</p>
</div>
<div class="section" id="credits">
<h1><a class="toc-backref" href="#id12">Credits</a></h1>
<div class="section" id="authors">
<h2><a class="toc-backref" href="#id13">Authors</a></h2>
<ul class="simple">
<li>Wakari</li>
</ul>
</div>
<div class="section" id="contributors">
<h2><a class="toc-backref" href="#id14">Contributors</a></h2>
<ul class="simple">
<li>François Degrave &lt;<a class="reference external" href="mailto:f.degrave&#64;wakari.be">f.degrave&#64;wakari.be</a>&gt;</li>
</ul>
</div>
<div class="section" id="maintainers">
<h2><a class="toc-backref" href="#id15">Maintainers</a></h2>
<p>This module is maintained by the OCA.</p>
<a class="reference external image-reference" href="https://odoo-community.org"><img alt="Odoo Community Association" src="https://odoo-community.org/logo.png" /></a>
<p>OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.</p>
<p>Current <a class="reference external" href="https://odoo-community.org/page/maintainer-role">maintainer</a>:</p>
<p><a class="reference external" href="https://github.com/fdegrave"><img alt="fdegrave" src="https://github.com/fdegrave.png?size=40px" /></a></p>
<p>This module is part of the <a class="reference external" href="https://github.com/OCA/rest-framework/tree/15.0/model_serializer">OCA/rest-framework</a> project on GitHub.</p>
<p>You are welcome to contribute. To learn how please visit <a class="reference external" href="https://odoo-community.org/page/Contribute">https://odoo-community.org/page/Contribute</a>.</p>
</div>
</div>
</div>
</body>
</html>
