===========================
REST Authentication Service
===========================

.. !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Alpha-red.png
    :target: https://odoo-community.org/page/development-status
    :alt: Alpha
.. |badge2| image:: https://img.shields.io/badge/licence-LGPL--3-blue.png
    :target: http://www.gnu.org/licenses/lgpl-3.0-standalone.html
    :alt: License: LGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Frest--framework-lightgray.png?logo=github
    :target: https://github.com/OCA/rest-framework/tree/15.0/base_rest_auth_user_service
    :alt: OCA/rest-framework
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/rest-framework-15-0/rest-framework-15-0-base_rest_auth_user_service
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runbot-Try%20me-875A7B.png
    :target: https://runbot.odoo-community.org/runbot/271/15.0
    :alt: Try me on Runbot

|badge1| |badge2| |badge3| |badge4| |badge5| 

This module adds API endpoints to deal with session authentication and logout.

.. IMPORTANT::
   This is an alpha version, the data model and design can change at any time without warning.
   Only for development or testing purpose, do not use in production.
   `More details on development status <https://odoo-community.org/page/development-status>`_

**Table of contents**

.. contents::
   :local:

Usage
=====

Authentication
~~~~~~~~~~~~~~

To authenticate you need to :code:`POST` a request on :code:`[ODOO HOST]/session/auth/login` with the
following body::

    {
        "db": [DB_NAME],
        "login": [LOGIN],
        "password": [PASSWORD]
    }

:code:`"db"` is not mandatory if Odoo is able to determine it unequivocally (e.g. single database server or
:code:`dbfilter` parameter). If the authentication is successful, the response will contain (in addition to the usual
response of the JSON-RPC authentication)::

    {
        ...
        "session": {
            "sid": "ff6b4bac7a590e7960abfc0ac38361433ecac1d6",
            "expires_at": "2021-09-21 16:53:56"
        }
    }

This :code:`sid` value can then be sent in subsequent requests in the following ways:

* header :code:`X-Openerp-Session-Id`
* cookie named `session_id`
* request param `session_id`

Logout
~~~~~~

To logout you need to :code:`POST` a request on :code:`[ODOO HOST]/session/auth/logout` with an empty body.

Changelog
=========

********.0
~~~~~~~~~~

First official version.

********.0
~~~~~~~~~~

Second version.

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/rest-framework/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us smashing it by providing a detailed and welcomed
`feedback <https://github.com/OCA/rest-framework/issues/new?body=module:%20base_rest_auth_user_service%0Aversion:%2015.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
~~~~~~~

* Wakari

Contributors
~~~~~~~~~~~~

* François Degrave <<EMAIL>>

Maintainers
~~~~~~~~~~~

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

This module is part of the `OCA/rest-framework <https://github.com/OCA/rest-framework/tree/15.0/base_rest_auth_user_service>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
