# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_rest
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: base_rest
#. odoo-python
#: code:addons/base_rest/restapi.py:0
#, python-format
msgid "%(key)'s JSON content is malformed: %(error)s"
msgstr ""

#. module: base_rest
#. odoo-python
#: code:addons/base_rest/restapi.py:0
#, python-format
msgid "BadRequest %s"
msgstr ""

#. module: base_rest
#. odoo-python
#: code:addons/base_rest/restapi.py:0
#, python-format
msgid "BadRequest item %(idx)s :%(errors)s"
msgstr ""

#. module: base_rest
#. odoo-python
#: code:addons/base_rest/restapi.py:0
#, python-format
msgid "BadRequest: Not enough items in the list (%(current)s < %(expected)s)"
msgstr ""

#. module: base_rest
#. odoo-python
#: code:addons/base_rest/restapi.py:0
#, python-format
msgid "BadRequest: Too many items in the list (%(current)s > %(expected)s)"
msgstr ""

#. module: base_rest
#: model:ir.ui.menu,name:base_rest.menu_rest_api_docs
msgid "Docs"
msgstr ""

#. module: base_rest
#. odoo-python
#: code:addons/base_rest/restapi.py:0
#, python-format
msgid "Invalid Response %s"
msgstr ""

#. module: base_rest
#: model:ir.actions.act_url,name:base_rest.action_rest_api_docs
#: model:ir.ui.menu,name:base_rest.menu_rest_api_root
msgid "REST API"
msgstr ""

#. module: base_rest
#: model:ir.model,name:base_rest.model_rest_service_registration
msgid "REST Services Registration Model"
msgstr ""

#. module: base_rest
#: model:ir.model,name:base_rest.model_ir_rule
msgid "Record Rule"
msgstr ""

#. module: base_rest
#. odoo-python
#: code:addons/base_rest/restapi.py:0
#, python-format
msgid "Unable to get cerberus schema from %s"
msgstr ""

#. module: base_rest
#. odoo-python
#: code:addons/base_rest/restapi.py:0
#, python-format
msgid "You must provide a dict of RestMethodParam"
msgstr ""
