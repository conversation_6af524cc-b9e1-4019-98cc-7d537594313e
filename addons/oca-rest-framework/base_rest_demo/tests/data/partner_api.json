{"info": {"description": "\nPartner Services\nAccess to the partner services is only allowed to authenticated users.\nIf you are not authenticated go to <a href='/web/login'>Login</a>\n", "title": "partner REST services", "version": ""}, "servers": [{"url": "http://localhost:8069/base_rest_demo_api/private/partner"}], "paths": {"/{id}/archive": {"post": {"summary": "\nArchive the given partner. This method is an empty method, IOW it\ndon't update the partner. This method is part of the demo data to\nillustrate that historically it's not mandatory to defined a schema\ndescribing the content of the response returned by a method.\nThis kind of definition is DEPRECATED and will no more supported in\nthe future.\n:param _id:\n:param params:\n:return:\n", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": [], "properties": {}}}}}, "responses": {"400": {"description": "One of the given parameter is not valid"}, "401": {"description": "The user is not authorized. Authentication is required"}, "404": {"description": "Requested resource not found"}, "403": {"description": "You don't have the permission to access the requested resource."}}, "security": [{"user": []}]}, "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer", "format": "int32"}}]}, "/create": {"post": {"summary": "\nCreate a new partner\n", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["city", "name", "street", "zip"], "properties": {"name": {"type": "string"}, "street": {"type": "string"}, "street2": {"nullable": true, "type": "string"}, "zip": {"type": "string"}, "city": {"type": "string"}, "phone": {"nullable": true, "type": "string"}, "state": {"type": "object", "required": [], "properties": {"id": {"nullable": true, "type": "integer"}, "name": {"type": "string"}}}, "country": {"type": "object", "required": ["id"], "properties": {"id": {"nullable": false, "type": "integer"}, "name": {"type": "string"}}}, "is_company": {"type": "boolean"}}}}}}, "responses": {"400": {"description": "One of the given parameter is not valid"}, "401": {"description": "The user is not authorized. Authentication is required"}, "404": {"description": "Requested resource not found"}, "403": {"description": "You don't have the permission to access the requested resource."}, "200": {"content": {"application/json": {"schema": {"type": "object", "required": ["city", "id", "name", "street", "zip"], "properties": {"name": {"type": "string"}, "street": {"type": "string"}, "street2": {"nullable": true, "type": "string"}, "zip": {"type": "string"}, "city": {"type": "string"}, "phone": {"nullable": true, "type": "string"}, "state": {"type": "object", "required": [], "properties": {"id": {"nullable": true, "type": "integer"}, "name": {"type": "string"}}}, "country": {"type": "object", "required": ["id"], "properties": {"id": {"nullable": false, "type": "integer"}, "name": {"type": "string"}}}, "is_company": {"type": "boolean"}, "id": {"type": "integer"}}}}}}}, "security": [{"user": []}]}}, "/": {"post": {"summary": "\nCreate a new partner\n", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["city", "name", "street", "zip"], "properties": {"name": {"type": "string"}, "street": {"type": "string"}, "street2": {"nullable": true, "type": "string"}, "zip": {"type": "string"}, "city": {"type": "string"}, "phone": {"nullable": true, "type": "string"}, "state": {"type": "object", "required": [], "properties": {"id": {"nullable": true, "type": "integer"}, "name": {"type": "string"}}}, "country": {"type": "object", "required": ["id"], "properties": {"id": {"nullable": false, "type": "integer"}, "name": {"type": "string"}}}, "is_company": {"type": "boolean"}}}}}}, "responses": {"400": {"description": "One of the given parameter is not valid"}, "401": {"description": "The user is not authorized. Authentication is required"}, "404": {"description": "Requested resource not found"}, "403": {"description": "You don't have the permission to access the requested resource."}, "200": {"content": {"application/json": {"schema": {"type": "object", "required": ["city", "id", "name", "street", "zip"], "properties": {"name": {"type": "string"}, "street": {"type": "string"}, "street2": {"nullable": true, "type": "string"}, "zip": {"type": "string"}, "city": {"type": "string"}, "phone": {"nullable": true, "type": "string"}, "state": {"type": "object", "required": [], "properties": {"id": {"nullable": true, "type": "integer"}, "name": {"type": "string"}}}, "country": {"type": "object", "required": ["id"], "properties": {"id": {"nullable": false, "type": "integer"}, "name": {"type": "string"}}}, "is_company": {"type": "boolean"}, "id": {"type": "integer"}}}}}}}, "security": [{"user": []}]}, "get": {"summary": "\nSearh partner by name\n", "parameters": [{"name": "name", "in": "query", "required": true, "allowEmptyValue": false, "default": null, "schema": {"type": "string"}}], "responses": {"400": {"description": "One of the given parameter is not valid"}, "401": {"description": "The user is not authorized. Authentication is required"}, "404": {"description": "Requested resource not found"}, "403": {"description": "You don't have the permission to access the requested resource."}, "200": {"content": {"application/json": {"schema": {"type": "object", "required": ["count", "rows"], "properties": {"count": {"type": "integer"}, "rows": {"type": "array", "items": {"type": "object", "required": ["city", "id", "name", "street", "zip"], "properties": {"name": {"type": "string"}, "street": {"type": "string"}, "street2": {"nullable": true, "type": "string"}, "zip": {"type": "string"}, "city": {"type": "string"}, "phone": {"nullable": true, "type": "string"}, "state": {"type": "object", "required": [], "properties": {"id": {"nullable": true, "type": "integer"}, "name": {"type": "string"}}}, "country": {"type": "object", "required": ["id"], "properties": {"id": {"nullable": false, "type": "integer"}, "name": {"type": "string"}}}, "is_company": {"type": "boolean"}, "id": {"type": "integer"}}}}}}}}}}, "security": [{"user": []}]}}, "/{id}/get": {"get": {"summary": "\nGet partner's informations\n", "responses": {"400": {"description": "One of the given parameter is not valid"}, "401": {"description": "The user is not authorized. Authentication is required"}, "404": {"description": "Requested resource not found"}, "403": {"description": "You don't have the permission to access the requested resource."}, "200": {"content": {"application/json": {"schema": {"type": "object", "required": ["city", "id", "name", "street", "zip"], "properties": {"name": {"type": "string"}, "street": {"type": "string"}, "street2": {"nullable": true, "type": "string"}, "zip": {"type": "string"}, "city": {"type": "string"}, "phone": {"nullable": true, "type": "string"}, "state": {"type": "object", "required": [], "properties": {"id": {"nullable": true, "type": "integer"}, "name": {"type": "string"}}}, "country": {"type": "object", "required": ["id"], "properties": {"id": {"nullable": false, "type": "integer"}, "name": {"type": "string"}}}, "is_company": {"type": "boolean"}, "id": {"type": "integer"}}}}}}}, "security": [{"user": []}]}, "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer", "format": "int32"}}]}, "/{id}": {"get": {"summary": "\nGet partner's informations\n", "responses": {"400": {"description": "One of the given parameter is not valid"}, "401": {"description": "The user is not authorized. Authentication is required"}, "404": {"description": "Requested resource not found"}, "403": {"description": "You don't have the permission to access the requested resource."}, "200": {"content": {"application/json": {"schema": {"type": "object", "required": ["city", "id", "name", "street", "zip"], "properties": {"name": {"type": "string"}, "street": {"type": "string"}, "street2": {"nullable": true, "type": "string"}, "zip": {"type": "string"}, "city": {"type": "string"}, "phone": {"nullable": true, "type": "string"}, "state": {"type": "object", "required": [], "properties": {"id": {"nullable": true, "type": "integer"}, "name": {"type": "string"}}}, "country": {"type": "object", "required": ["id"], "properties": {"id": {"nullable": false, "type": "integer"}, "name": {"type": "string"}}}, "is_company": {"type": "boolean"}, "id": {"type": "integer"}}}}}}}, "security": [{"user": []}]}, "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer", "format": "int32"}}], "post": {"summary": "\nUpdate partner informations\n", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": [], "properties": {"name": {"type": "string"}, "street": {"type": "string"}, "street2": {"nullable": true, "type": "string"}, "zip": {"type": "string"}, "city": {"type": "string"}, "phone": {"nullable": true, "type": "string"}, "state": {"type": "object", "required": [], "properties": {"id": {"nullable": true, "type": "integer"}, "name": {"type": "string"}}}, "country": {"type": "object", "required": ["id"], "properties": {"id": {"nullable": false, "type": "integer"}, "name": {"type": "string"}}}, "is_company": {"type": "boolean"}}}}}}, "responses": {"400": {"description": "One of the given parameter is not valid"}, "401": {"description": "The user is not authorized. Authentication is required"}, "404": {"description": "Requested resource not found"}, "403": {"description": "You don't have the permission to access the requested resource."}, "200": {"content": {"application/json": {"schema": {"type": "object", "required": ["city", "id", "name", "street", "zip"], "properties": {"name": {"type": "string"}, "street": {"type": "string"}, "street2": {"nullable": true, "type": "string"}, "zip": {"type": "string"}, "city": {"type": "string"}, "phone": {"nullable": true, "type": "string"}, "state": {"type": "object", "required": [], "properties": {"id": {"nullable": true, "type": "integer"}, "name": {"type": "string"}}}, "country": {"type": "object", "required": ["id"], "properties": {"id": {"nullable": false, "type": "integer"}, "name": {"type": "string"}}}, "is_company": {"type": "boolean"}, "id": {"type": "integer"}}}}}}}, "security": [{"user": []}]}, "put": {"summary": "\nUpdate partner informations\n", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": [], "properties": {"name": {"type": "string"}, "street": {"type": "string"}, "street2": {"nullable": true, "type": "string"}, "zip": {"type": "string"}, "city": {"type": "string"}, "phone": {"nullable": true, "type": "string"}, "state": {"type": "object", "required": [], "properties": {"id": {"nullable": true, "type": "integer"}, "name": {"type": "string"}}}, "country": {"type": "object", "required": ["id"], "properties": {"id": {"nullable": false, "type": "integer"}, "name": {"type": "string"}}}, "is_company": {"type": "boolean"}}}}}}, "responses": {"400": {"description": "One of the given parameter is not valid"}, "401": {"description": "The user is not authorized. Authentication is required"}, "404": {"description": "Requested resource not found"}, "403": {"description": "You don't have the permission to access the requested resource."}, "200": {"content": {"application/json": {"schema": {"type": "object", "required": ["city", "id", "name", "street", "zip"], "properties": {"name": {"type": "string"}, "street": {"type": "string"}, "street2": {"nullable": true, "type": "string"}, "zip": {"type": "string"}, "city": {"type": "string"}, "phone": {"nullable": true, "type": "string"}, "state": {"type": "object", "required": [], "properties": {"id": {"nullable": true, "type": "integer"}, "name": {"type": "string"}}}, "country": {"type": "object", "required": ["id"], "properties": {"id": {"nullable": false, "type": "integer"}, "name": {"type": "string"}}}, "is_company": {"type": "boolean"}, "id": {"type": "integer"}}}}}}}, "security": [{"user": []}]}}, "/search": {"get": {"summary": "\nSearh partner by name\n", "parameters": [{"name": "name", "in": "query", "required": true, "allowEmptyValue": false, "default": null, "schema": {"type": "string"}}], "responses": {"400": {"description": "One of the given parameter is not valid"}, "401": {"description": "The user is not authorized. Authentication is required"}, "404": {"description": "Requested resource not found"}, "403": {"description": "You don't have the permission to access the requested resource."}, "200": {"content": {"application/json": {"schema": {"type": "object", "required": ["count", "rows"], "properties": {"count": {"type": "integer"}, "rows": {"type": "array", "items": {"type": "object", "required": ["city", "id", "name", "street", "zip"], "properties": {"name": {"type": "string"}, "street": {"type": "string"}, "street2": {"nullable": true, "type": "string"}, "zip": {"type": "string"}, "city": {"type": "string"}, "phone": {"nullable": true, "type": "string"}, "state": {"type": "object", "required": [], "properties": {"id": {"nullable": true, "type": "integer"}, "name": {"type": "string"}}}, "country": {"type": "object", "required": ["id"], "properties": {"id": {"nullable": false, "type": "integer"}, "name": {"type": "string"}}}, "is_company": {"type": "boolean"}, "id": {"type": "integer"}}}}}}}}}}, "security": [{"user": []}]}}, "/{id}/update": {"post": {"summary": "\nUpdate partner informations\n", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": [], "properties": {"name": {"type": "string"}, "street": {"type": "string"}, "street2": {"nullable": true, "type": "string"}, "zip": {"type": "string"}, "city": {"type": "string"}, "phone": {"nullable": true, "type": "string"}, "state": {"type": "object", "required": [], "properties": {"id": {"nullable": true, "type": "integer"}, "name": {"type": "string"}}}, "country": {"type": "object", "required": ["id"], "properties": {"id": {"nullable": false, "type": "integer"}, "name": {"type": "string"}}}, "is_company": {"type": "boolean"}}}}}}, "responses": {"400": {"description": "One of the given parameter is not valid"}, "401": {"description": "The user is not authorized. Authentication is required"}, "404": {"description": "Requested resource not found"}, "403": {"description": "You don't have the permission to access the requested resource."}, "200": {"content": {"application/json": {"schema": {"type": "object", "required": ["city", "id", "name", "street", "zip"], "properties": {"name": {"type": "string"}, "street": {"type": "string"}, "street2": {"nullable": true, "type": "string"}, "zip": {"type": "string"}, "city": {"type": "string"}, "phone": {"nullable": true, "type": "string"}, "state": {"type": "object", "required": [], "properties": {"id": {"nullable": true, "type": "integer"}, "name": {"type": "string"}}}, "country": {"type": "object", "required": ["id"], "properties": {"id": {"nullable": false, "type": "integer"}, "name": {"type": "string"}}}, "is_company": {"type": "boolean"}, "id": {"type": "integer"}}}}}}}, "security": [{"user": []}]}, "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer", "format": "int32"}}]}}, "openapi": "3.0.0", "components": {"securitySchemes": {"user": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "cookie", "name": "session_id"}}}}