{"info": {"description": "\nPartner New API Services\nServices developed with the new api provided by base_rest and pydantic\n", "title": "partner_pydantic REST services", "version": ""}, "servers": [{"url": "http://localhost:8069/base_rest_demo_api/new_api/partner_pydantic"}], "paths": {"/{id}/get": {"get": {"summary": "\nGet partner's information\n", "responses": {"400": {"description": "One of the given parameter is not valid"}, "401": {"description": "The user is not authorized. Authentication is required"}, "404": {"description": "Requested resource not found"}, "403": {"description": "You don't have the permission to access the requested resource."}, "200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PartnerInfo"}}}}}}, "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer", "format": "int32"}}]}, "/{id}": {"get": {"summary": "\nGet partner's information\n", "responses": {"400": {"description": "One of the given parameter is not valid"}, "401": {"description": "The user is not authorized. Authentication is required"}, "404": {"description": "Requested resource not found"}, "403": {"description": "You don't have the permission to access the requested resource."}, "200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PartnerInfo"}}}}}}, "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer", "format": "int32"}}]}, "/": {"get": {"summary": "\nSearch for partners\n:param partner_search_param: An instance of partner.search.param\n:return: List of partner.short.info\n", "parameters": [{"name": "id", "in": "query", "required": false, "allowEmptyValue": false, "default": null, "schema": {"type": "integer"}}, {"name": "name", "in": "query", "required": false, "allowEmptyValue": false, "default": null, "schema": {"type": "string"}}], "responses": {"400": {"description": "One of the given parameter is not valid"}, "401": {"description": "The user is not authorized. Authentication is required"}, "404": {"description": "Requested resource not found"}, "403": {"description": "You don't have the permission to access the requested resource."}, "200": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PartnerShortInfo"}}}}}}}}, "/search": {"get": {"summary": "\nSearch for partners\n:param partner_search_param: An instance of partner.search.param\n:return: List of partner.short.info\n", "parameters": [{"name": "id", "in": "query", "required": false, "allowEmptyValue": false, "default": null, "schema": {"type": "integer"}}, {"name": "name", "in": "query", "required": false, "allowEmptyValue": false, "default": null, "schema": {"type": "string"}}], "responses": {"400": {"description": "One of the given parameter is not valid"}, "401": {"description": "The user is not authorized. Authentication is required"}, "404": {"description": "Requested resource not found"}, "403": {"description": "You don't have the permission to access the requested resource."}, "200": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PartnerShortInfo"}}}}}}}}}, "openapi": "3.0.0", "components": {"schemas": {"CountryInfo": {"properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}}, "required": ["id", "name"], "title": "CountryInfo", "type": "object"}, "StateInfo": {"properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}}, "required": ["id", "name"], "title": "StateInfo", "type": "object"}, "PartnerInfo": {"properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "street": {"title": "Street", "type": "string"}, "street2": {"default": null, "title": "Street2", "type": "string"}, "zip_code": {"title": "Zip Code", "type": "string"}, "city": {"title": "City", "type": "string"}, "phone": {"default": null, "title": "Phone", "type": "string"}, "state": {"$ref": "#/components/schemas/StateInfo"}, "country": {"$ref": "#/components/schemas/CountryInfo"}, "is_company": {"default": null, "title": "Is Company", "type": "boolean"}}, "required": ["id", "name", "street", "zip_code", "city", "state", "country"], "title": "PartnerInfo", "type": "object"}, "PartnerShortInfo": {"properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}}, "required": ["id", "name"], "title": "PartnerShortInfo", "type": "object"}}, "securitySchemes": {"user": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "cookie", "name": "session_id"}}}}