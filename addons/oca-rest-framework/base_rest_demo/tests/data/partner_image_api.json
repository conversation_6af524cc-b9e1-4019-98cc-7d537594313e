{"info": {"description": "\nPartner Image Services\n\nService used to retrieve the partner's image\nAccess to the partner image service is only allowed to authenticated\nusers.\nIf you are not authenticated go to <a href='/web/login'>Login</a>\n", "title": "partner_image REST services", "version": ""}, "servers": [{"url": "http://localhost:8069/base_rest_demo_api/private/partner_image"}], "paths": {"/{id}/get": {"get": {"summary": "\nGet partner's image\n", "parameters": [{"name": "size", "in": "query", "required": false, "allowEmptyValue": false, "default": "small", "schema": {"type": "string", "enum": ["small", "medium", "large"]}}], "responses": {"400": {"description": "One of the given parameter is not valid"}, "401": {"description": "The user is not authorized. Authentication is required"}, "404": {"description": "Requested resource not found"}, "403": {"description": "You don't have the permission to access the requested resource."}}, "security": [{"user": []}]}, "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer", "format": "int32"}}]}, "/{id}": {"get": {"summary": "\nGet partner's image\n", "parameters": [{"name": "size", "in": "query", "required": false, "allowEmptyValue": false, "default": "small", "schema": {"type": "string", "enum": ["small", "medium", "large"]}}], "responses": {"400": {"description": "One of the given parameter is not valid"}, "401": {"description": "The user is not authorized. Authentication is required"}, "404": {"description": "Requested resource not found"}, "403": {"description": "You don't have the permission to access the requested resource."}}, "security": [{"user": []}]}, "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer", "format": "int32"}}]}}, "openapi": "3.0.0", "components": {"securitySchemes": {"user": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "cookie", "name": "session_id"}}}}