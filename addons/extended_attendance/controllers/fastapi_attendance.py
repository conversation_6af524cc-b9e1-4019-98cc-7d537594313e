from odoo.addons.fastapi.dependencies import odoo_env
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List, Optional

# Pydantic models for automatic Swagger generation
class PersonType(BaseModel):
    id: int
    name: str
    description: Optional[str] = None
    access_level: str
    active: bool

class PersonTypeCreate(BaseModel):
    name: str
    description: Optional[str] = None
    access_level: str = "basic"

# Create router - this integrates with Odoo's FastAPI
router = APIRouter(tags=["Person Types"])

@router.get("/person-types", response_model=List[PersonType])
def get_person_types(env=Depends(odoo_env)):
    """
    Get all person types
    
    Returns a list of all available person types in the system.
    Each person type defines access levels and permissions.
    """
    try:
        # Same logic as your existing controller!
        person_types = env['person.type'].search([])
        data = []
        
        for person_type in person_types:
            data.append(PersonType(
                id=person_type.id,
                name=person_type.name,
                description=person_type.description,
                access_level=person_type.access_level,
                active=person_type.active
            ))
        
        return data
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/person-types", response_model=PersonType)
def create_person_type(person_type_data: PersonTypeCreate, env=Depends(odoo_env)):
    """
    Create a new person type
    
    Creates a new person type with the specified name, description, and access level.
    """
    try:
        # Convert your existing create logic
        new_person_type = env['person.type'].create({
            'name': person_type_data.name,
            'description': person_type_data.description,
            'access_level': person_type_data.access_level,
        })
        
        return PersonType(
            id=new_person_type.id,
            name=new_person_type.name,
            description=new_person_type.description,
            access_level=new_person_type.access_level,
            active=new_person_type.active
        )
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/person-types/{type_id}", response_model=PersonType)
def get_person_type(type_id: int, env=Depends(odoo_env)):
    """
    Get a specific person type by ID
    """
    try:
        person_type = env['person.type'].browse(type_id)
        
        if not person_type.exists():
            raise HTTPException(status_code=404, detail="Person type not found")
        
        return PersonType(
            id=person_type.id,
            name=person_type.name,
            description=person_type.description,
            access_level=person_type.access_level,
            active=person_type.active
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
