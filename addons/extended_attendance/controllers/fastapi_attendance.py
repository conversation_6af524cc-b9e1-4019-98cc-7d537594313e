from odoo import http
from odoo.http import request
import json


class AttendanceController(http.Controller):

    @http.route('/api/person-types', type='http', auth='public', methods=['GET'], csrf=False)
    def get_person_types(self, **kwargs):
        """Get all person types"""
        try:
            person_types = request.env['person.type'].sudo().search([])
            data = []

            for person_type in person_types:
                data.append({
                    'id': person_type.id,
                    'name': person_type.name,
                    'description': person_type.description or '',
                    'access_level': person_type.access_level,
                    'active': person_type.active
                })

            response_data = {
                'success': True,
                'data': data,
                'count': len(data)
            }

            return request.make_response(
                json.dumps(response_data),
                headers=[('Content-Type', 'application/json')]
            )

        except Exception as e:
            error_data = {
                'success': False,
                'error': str(e)
            }
            return request.make_response(
                json.dumps(error_data),
                headers=[('Content-Type', 'application/json')]
            )

    @http.route('/api/person-types', type='http', auth='public', methods=['POST'], csrf=False)
    def create_person_type(self, **kwargs):
        """Create a new person type"""
        try:
            data = json.loads(request.httprequest.data.decode('utf-8'))

            new_person_type = request.env['person.type'].sudo().create({
                'name': data.get('name'),
                'description': data.get('description', ''),
                'access_level': data.get('access_level', 'basic'),
            })

            return json.dumps({
                'success': True,
                'data': {
                    'id': new_person_type.id,
                    'name': new_person_type.name,
                    'description': new_person_type.description or '',
                    'access_level': new_person_type.access_level,
                    'active': new_person_type.active
                }
            })

        except Exception as e:
            return json.dumps({
                'success': False,
                'error': str(e)
            })

    @http.route('/api/person-types/<int:type_id>', type='http', auth='public', methods=['GET'], csrf=False)
    def get_person_type(self, type_id, **kwargs):
        """Get a specific person type by ID"""
        try:
            person_type = request.env['person.type'].sudo().browse(type_id)

            if not person_type.exists():
                return json.dumps({
                    'success': False,
                    'error': 'Person type not found'
                })

            return json.dumps({
                'success': True,
                'data': {
                    'id': person_type.id,
                    'name': person_type.name,
                    'description': person_type.description or '',
                    'access_level': person_type.access_level,
                    'active': person_type.active
                }
            })

        except Exception as e:
            return json.dumps({
                'success': False,
                'error': str(e)
            })

    @http.route('/api/locations', type='http', auth='public', methods=['GET'], csrf=False)
    def get_locations(self, **kwargs):
        """Get all locations"""
        try:
            locations = request.env['attendance.location'].sudo().search([('active', '=', True)])
            data = []

            for location in locations:
                data.append({
                    'id': location.id,
                    'name': location.name,
                    'code': location.code,
                    'capacity': location.capacity,
                    'current_occupancy': location.current_occupancy,
                    'building': location.building or '',
                    'floor': location.floor or '',
                    'active': location.active
                })

            return json.dumps({
                'success': True,
                'data': data,
                'count': len(data)
            })

        except Exception as e:
            return json.dumps({
                'success': False,
                'error': str(e)
            })

    @http.route('/api/check-in', type='http', auth='public', methods=['POST'], csrf=False)
    def check_in(self, **kwargs):
        """Check in a person"""
        try:
            data = json.loads(request.httprequest.data.decode('utf-8'))
            person_identifier = data.get('person_identifier')
            location_code = data.get('location_code')

            if not person_identifier or not location_code:
                return json.dumps({
                    'success': False,
                    'error': 'person_identifier and location_code are required'
                })

            # Find person
            person = request.env['extended.attendance.person'].sudo().search_by_identifier(person_identifier)
            if not person:
                return json.dumps({
                    'success': False,
                    'error': f'Person not found with identifier: {person_identifier}'
                })

            # Find location
            location = request.env['attendance.location'].sudo().search([('code', '=', location_code)], limit=1)
            if not location:
                return json.dumps({
                    'success': False,
                    'error': f'Location not found with code: {location_code}'
                })

            # Create attendance record
            attendance = person.create_attendance(location.id, 'check_in')

            return json.dumps({
                'success': True,
                'message': f'Successfully checked in {person.name} at {location.name}',
                'data': {
                    'id': attendance.id,
                    'person_name': attendance.person_name,
                    'location_name': attendance.location_name,
                    'check_in': attendance.check_in.isoformat(),
                    'state': attendance.state,
                }
            })

        except Exception as e:
            return json.dumps({
                'success': False,
                'error': str(e)
            })
