import json
import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any

from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel, Field

from odoo import fields, _
from odoo.exceptions import ValidationError, UserError
from odoo.addons.fastapi.dependencies import odoo_env

_logger = logging.getLogger(__name__)

# Pydantic Models for Request/Response validation and Swagger docs
class StandardResponse(BaseModel):
    """Standard API response format"""
    success: bool
    timestamp: str
    data: Optional[Dict[str, Any]] = None
    message: Optional[str] = None
    error: Optional[str] = None

class PersonTypeResponse(BaseModel):
    """Person type data model"""
    id: int
    name: str
    description: Optional[str] = None
    access_level: str
    color: Optional[str] = None
    active: bool

class PersonTypeCreate(BaseModel):
    """Create person type request"""
    name: str = Field(..., description="Person type name")
    description: Optional[str] = Field(None, description="Description")
    access_level: str = Field("basic", description="Access level")
    color: Optional[str] = Field(None, description="Color code")

class LocationResponse(BaseModel):
    """Location data model"""
    id: int
    name: str
    code: str
    building: Optional[str] = None
    floor: Optional[str] = None
    capacity: Optional[int] = None
    current_occupancy: int
    active: bool

class LocationCreate(BaseModel):
    """Create location request"""
    name: str = Field(..., description="Location name")
    code: str = Field(..., description="Location code")
    building: Optional[str] = Field(None, description="Building name")
    floor: Optional[str] = Field(None, description="Floor")
    capacity: Optional[int] = Field(None, description="Maximum capacity")

class PersonResponse(BaseModel):
    """Person data model"""
    id: int
    name: str
    person_id: str
    email: Optional[str] = None
    person_type_id: Optional[int] = None
    person_type_name: Optional[str] = None
    access_level: str
    active: bool

class PersonCreate(BaseModel):
    """Create person request"""
    name: str = Field(..., description="Full name")
    person_id: str = Field(..., description="Person ID")
    email: Optional[str] = Field(None, description="Email address")
    person_type_id: int = Field(..., description="Person type ID")
    access_level: str = Field("basic", description="Access level")

class AttendanceResponse(BaseModel):
    """Attendance record data model"""
    id: int
    person_name: str
    location_name: str
    check_in: str
    check_out: Optional[str] = None
    state: str
    worked_hours: Optional[float] = None

class CheckInRequest(BaseModel):
    """Check-in request"""
    person_identifier: str = Field(..., description="Person ID, barcode, RFID, or QR code")
    location_code: str = Field(..., description="Location code")
    device_id: Optional[str] = Field(None, description="Device ID")
    check_in_time: Optional[str] = Field(None, description="Custom check-in time")

class CheckOutRequest(BaseModel):
    """Check-out request"""
    person_identifier: str = Field(..., description="Person ID, barcode, RFID, or QR code")
    location_code: str = Field(..., description="Location code")
    device_id: Optional[str] = Field(None, description="Device ID")
    check_out_time: Optional[str] = Field(None, description="Custom check-out time")

# Create FastAPI router
router = APIRouter(prefix="/api/attendance", tags=["Extended Attendance API"])

def get_standard_response(success=True, data=None, message=None, error=None):
    """Standard response format"""
    response = StandardResponse(
        success=success,
        timestamp=fields.Datetime.now().isoformat(),
        data=data,
        message=message,
        error=error
    )
    return response.dict()

def handle_exception(e):
    """Handle exceptions and return appropriate response"""
    _logger.error(f"API Error: {str(e)}")

    if isinstance(e, (ValidationError, UserError)):
        raise HTTPException(status_code=400, detail=str(e))
    else:
        raise HTTPException(status_code=500, detail="Internal server error")

# Person Types API Endpoints
@router.get("/person-types")
def get_person_types(env=Depends(odoo_env)):
    """
    Get all person types

    Returns a list of all available person types in the system.
    Each person type defines access levels and permissions for different user categories.
    """
    try:
        person_types = env['extended.attendance.person.type'].search([])
        data = []

        for pt in person_types:
            data.append({
                'id': pt.id,
                'name': pt.name,
                'code': pt.code,
                'description': pt.description,
                'sequence': pt.sequence,
                'active': pt.active,
                'is_system': pt.is_system,
                'can_delete': pt.can_delete,
                'person_count': pt.person_count,
                'requires_approval': pt.requires_approval,
                'default_access_level': pt.default_access_level,
                'max_duration_hours': pt.max_duration_hours,
            })

        return get_standard_response(data=data)

    except Exception as e:
        handle_exception(e)

@router.post("/person-types")
def create_person_type(person_type_data: dict, env=Depends(odoo_env)):
    """
    Create a new person type

    Creates a new person type with the specified properties.
    Required fields: name, code
    """
    try:
        required_fields = ['name', 'code']
        for field in required_fields:
            if field not in person_type_data:
                raise HTTPException(status_code=400, detail=f"Missing required field: {field}")

        person_type = env['extended.attendance.person.type'].create(person_type_data)

        return get_standard_response(
            data={'id': person_type.id, 'name': person_type.name},
            message=f"Person type '{person_type.name}' created successfully"
        )

    except Exception as e:
        handle_exception(e)

@router.put("/person-types/{type_id}")
def update_person_type(type_id: int, person_type_data: dict, env=Depends(odoo_env)):
    """
    Update a person type

    Updates an existing person type with the provided data.
    """
    try:
        person_type = env['extended.attendance.person.type'].browse(type_id)
        if not person_type.exists():
            raise HTTPException(status_code=404, detail="Person type not found")

        person_type.write(person_type_data)

        return get_standard_response(
            data={'id': person_type.id, 'name': person_type.name},
            message=f"Person type '{person_type.name}' updated successfully"
        )

    except Exception as e:
        handle_exception(e)

@router.delete("/person-types/{type_id}")
def delete_person_type(type_id: int, env=Depends(odoo_env)):
    """
    Delete a person type

    Permanently deletes a person type from the system.
    """
    try:
        person_type = env['extended.attendance.person.type'].browse(type_id)
        if not person_type.exists():
            raise HTTPException(status_code=404, detail="Person type not found")

        name = person_type.name
        person_type.unlink()

        return get_standard_response(message=f"Person type '{name}' deleted successfully")

    except Exception as e:
        handle_exception(e)

# Locations API Endpoints
@router.get("/locations")
def get_locations(env=Depends(odoo_env)):
    """
    Get all locations

    Returns a list of all attendance locations with their current status,
    occupancy, and operational information.
    """
    try:
        locations = env['extended.attendance.location'].search([])
        data = []

        for loc in locations:
            data.append({
                'id': loc.id,
                'name': loc.name,
                'code': loc.code,
                'description': loc.description,
                'sequence': loc.sequence,
                'active': loc.active,
                'building': loc.building,
                'floor': loc.floor,
                'room_number': loc.room_number,
                'capacity': loc.capacity,
                'current_occupancy': loc.current_occupancy,
                'parent_location_id': loc.parent_location_id.id if loc.parent_location_id else None,
                'parent_location_name': loc.parent_location_id.name if loc.parent_location_id else None,
                'location_path': loc.location_path,
                'requires_permission': loc.requires_permission,
                'has_operating_hours': loc.has_operating_hours,
                'is_operating_now': loc.is_operating_now(),
            })

        return get_standard_response(data=data)

    except Exception as e:
        handle_exception(e)

    @http.route('/api/attendance/locations', type='json', auth='user', methods=['POST'])
    def create_location(self, **kwargs):
        """Create a new location"""
        try:
            required_fields = ['name', 'code']
            for field in required_fields:
                if field not in kwargs:
                    return self._get_response(success=False, error=f"Missing required field: {field}")
            
            location = request.env['extended.attendance.location'].create(kwargs)
            
            return self._get_response(
                data={'id': location.id, 'name': location.name, 'code': location.code},
                message=f"Location '{location.name}' created successfully"
            )
            
        except Exception as e:
            return self._handle_exception(e)

    @http.route('/api/attendance/locations/<int:location_id>', type='json', auth='user', methods=['PUT'])
    def update_location(self, location_id, **kwargs):
        """Update a location"""
        try:
            location = request.env['extended.attendance.location'].browse(location_id)
            if not location.exists():
                return self._get_response(success=False, error="Location not found")
            
            location.write(kwargs)
            
            return self._get_response(
                data={'id': location.id, 'name': location.name},
                message=f"Location '{location.name}' updated successfully"
            )
            
        except Exception as e:
            return self._handle_exception(e)

    @http.route('/api/attendance/locations/<int:location_id>', type='json', auth='user', methods=['DELETE'])
    def delete_location(self, location_id):
        """Delete a location"""
        try:
            location = request.env['extended.attendance.location'].browse(location_id)
            if not location.exists():
                return self._get_response(success=False, error="Location not found")
            
            name = location.name
            location.unlink()
            
            return self._get_response(message=f"Location '{name}' deleted successfully")
            
        except Exception as e:
            return self._handle_exception(e)

    # Persons API
    @http.route('/api/attendance/persons', type='json', auth='user', methods=['GET'])
    def get_persons(self, **kwargs):
        """Get all persons with optional filtering"""
        try:
            domain = []
            
            # Add filters
            if kwargs.get('person_type_code'):
                person_type = request.env['extended.attendance.person.type'].search([
                    ('code', '=', kwargs['person_type_code'])
                ], limit=1)
                if person_type:
                    domain.append(('person_type_id', '=', person_type.id))
            
            if kwargs.get('active') is not None:
                domain.append(('active', '=', kwargs['active']))
            
            persons = request.env['extended.attendance.person'].search(domain)
            data = []
            
            for person in persons:
                data.append({
                    'id': person.id,
                    'name': person.name,
                    'person_id': person.person_id,
                    'person_type': {
                        'id': person.person_type_id.id,
                        'name': person.person_type_id.name,
                        'code': person.person_type_id.code,
                    },
                    'email': person.email,
                    'phone': person.phone,
                    'active': person.active,
                    'access_level': person.access_level,
                    'is_checked_in': person.is_checked_in,
                    'current_location': {
                        'id': person.current_location_id.id,
                        'name': person.current_location_id.name,
                        'code': person.current_location_id.code,
                    } if person.current_location_id else None,
                    'last_attendance': person.last_attendance.isoformat() if person.last_attendance else None,
                    'attendance_count': person.attendance_count,
                })
            
            return self._get_response(data=data)

        except Exception as e:
            return self._handle_exception(e)

    @http.route('/api/attendance/persons', type='json', auth='user', methods=['POST'])
    def create_person(self, **kwargs):
        """Create a new person"""
        try:
            required_fields = ['name', 'person_id', 'person_type_id']
            for field in required_fields:
                if field not in kwargs:
                    return self._get_response(success=False, error=f"Missing required field: {field}")

            person = request.env['extended.attendance.person'].create(kwargs)

            return self._get_response(
                data={'id': person.id, 'name': person.name, 'person_id': person.person_id},
                message=f"Person '{person.name}' created successfully"
            )

        except Exception as e:
            return self._handle_exception(e)

    @http.route('/api/attendance/persons/<int:person_id>', type='json', auth='user', methods=['PUT'])
    def update_person(self, person_id, **kwargs):
        """Update a person"""
        try:
            person = request.env['extended.attendance.person'].browse(person_id)
            if not person.exists():
                return self._get_response(success=False, error="Person not found")

            person.write(kwargs)

            return self._get_response(
                data={'id': person.id, 'name': person.name},
                message=f"Person '{person.name}' updated successfully"
            )

        except Exception as e:
            return self._handle_exception(e)

    @http.route('/api/attendance/persons/<int:person_id>', type='json', auth='user', methods=['DELETE'])
    def delete_person(self, person_id):
        """Delete a person"""
        try:
            person = request.env['extended.attendance.person'].browse(person_id)
            if not person.exists():
                return self._get_response(success=False, error="Person not found")

            name = person.name
            person.unlink()

            return self._get_response(message=f"Person '{name}' deleted successfully")

        except Exception as e:
            return self._handle_exception(e)

    @http.route('/api/attendance/persons/search', type='json', auth='user', methods=['POST'])
    def search_person(self, identifier):
        """Search person by identifier"""
        try:
            person = request.env['extended.attendance.person'].search_by_identifier(identifier)

            if not person:
                return self._get_response(success=False, error=f"Person not found with identifier: {identifier}")

            data = {
                'id': person.id,
                'name': person.name,
                'person_id': person.person_id,
                'person_type': {
                    'id': person.person_type_id.id,
                    'name': person.person_type_id.name,
                    'code': person.person_type_id.code,
                },
                'active': person.active,
                'is_checked_in': person.is_checked_in,
                'current_location': {
                    'id': person.current_location_id.id,
                    'name': person.current_location_id.name,
                    'code': person.current_location_id.code,
                } if person.current_location_id else None,
            }

            return self._get_response(data=data)

        except Exception as e:
            handle_exception(e)

# Attendance API Endpoints
@router.post("/check-in")
def check_in(request: CheckInRequest, env=Depends(odoo_env)):
    """
    Check in a person at a location

    This endpoint allows checking in a person using various identifiers:
    - Person ID
    - Barcode
    - RFID tag
    - QR code

    Returns attendance record with person and location details.
    """
    try:
        # Parse check_in_time if provided
        check_in_time = None
        if request.check_in_time:
            check_in_time = fields.Datetime.from_string(request.check_in_time)

        attendance = env['extended.attendance.record'].create_check_in(
            request.person_identifier, request.location_code, request.device_id, check_in_time
        )

        data = {
            'id': attendance.id,
            'person_name': attendance.person_name,
            'location_name': attendance.location_name,
            'check_in': attendance.check_in.isoformat(),
            'state': attendance.state,
        }

        return get_standard_response(
            data=data,
            message=f"Successfully checked in {attendance.person_name} at {attendance.location_name}"
        )

    except Exception as e:
        handle_exception(e)

@router.post("/check-out")
def check_out(request: CheckOutRequest, env=Depends(odoo_env)):
    """
    Check out a person from their current location

    This endpoint allows checking out a person using various identifiers.
    The person must have an active check-in to be checked out.
    """
    try:
        # Parse check_out_time if provided
        check_out_time = None
        if request.check_out_time:
            check_out_time = fields.Datetime.from_string(request.check_out_time)

        attendance = env['extended.attendance.record'].create_check_out(
            request.person_identifier, check_out_time
        )

        data = {
            'id': attendance.id,
            'person_name': attendance.person_name,
            'location_name': attendance.location_name,
            'check_in': attendance.check_in.isoformat(),
            'check_out': attendance.check_out.isoformat(),
            'worked_hours': attendance.worked_hours,
            'duration_display': attendance.duration_display,
            'state': attendance.state,
        }

        return get_standard_response(
            data=data,
            message=f"Successfully checked out {attendance.person_name} from {attendance.location_name}"
        )

    except Exception as e:
        handle_exception(e)

@router.get("/current")
def get_current_attendance(location_code: Optional[str] = Query(None, description="Filter by location code"), env=Depends(odoo_env)):
    """
    Get current attendance (people currently checked in)

    Returns a list of people who are currently checked in.
    Optionally filter by location code.
    """
    try:
        attendances = env['extended.attendance.record'].get_current_attendance(location_code)
        data = []

        for att in attendances:
            data.append({
                'id': att.id,
                'person_name': att.person_name,
                'person_id': att.person_id.person_id,
                'person_type': att.person_type_id.name,
                'location_name': att.location_name,
                'location_code': att.location_id.code,
                'check_in': att.check_in.isoformat(),
                'duration_display': att.duration_display,
            })

        return get_standard_response(data=data)

    except Exception as e:
        handle_exception(e)

# Additional endpoints can be added here following the same pattern
# The FastAPI router will automatically generate Swagger documentation
