<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Default Person Types -->
        <record id="person_type_admin" model="person.type">
            <field name="name">Administrator</field>
            <field name="code">ADMIN</field>
            <field name="description">System administrators with full access</field>
            <field name="sequence">1</field>
            <field name="is_system" eval="True"/>
            <field name="default_access_level">full</field>
            <field name="color">1</field>
        </record>

        <record id="person_type_owner" model="person.type">
            <field name="name">Owner</field>
            <field name="code">OWNER</field>
            <field name="description">Organization owners with full access</field>
            <field name="sequence">2</field>
            <field name="is_system" eval="True"/>
            <field name="default_access_level">full</field>
            <field name="color">2</field>
        </record>

        <record id="person_type_employee" model="person.type">
            <field name="name">Employee</field>
            <field name="code">EMP</field>
            <field name="description">Regular employees</field>
            <field name="sequence">10</field>
            <field name="default_access_level">standard</field>
            <field name="color">3</field>
        </record>

        <record id="person_type_student" model="person.type">
            <field name="name">Student</field>
            <field name="code">STU</field>
            <field name="description">Students</field>
            <field name="sequence">20</field>
            <field name="default_access_level">basic</field>
            <field name="color">4</field>
        </record>

        <record id="person_type_guest" model="person.type">
            <field name="name">Guest</field>
            <field name="code">GST</field>
            <field name="description">Temporary guests</field>
            <field name="sequence">30</field>
            <field name="requires_approval" eval="True"/>
            <field name="default_access_level">restricted</field>
            <field name="max_duration_hours">8.0</field>
            <field name="color">5</field>
        </record>

        <!-- Default Locations -->
        <record id="location_main_entrance" model="attendance.location">
            <field name="name">Main Entrance</field>
            <field name="code">MAIN_ENT</field>
            <field name="description">Primary entrance to the building</field>
            <field name="sequence">1</field>
            <field name="color">1</field>
        </record>

        <record id="location_reception" model="attendance.location">
            <field name="name">Reception</field>
            <field name="code">RECEPTION</field>
            <field name="description">Reception area</field>
            <field name="sequence">2</field>
            <field name="color">2</field>
        </record>

        <record id="location_office" model="attendance.location">
            <field name="name">Office Area</field>
            <field name="code">OFFICE</field>
            <field name="description">General office workspace</field>
            <field name="sequence">10</field>
            <field name="color">3</field>
        </record>

        <record id="location_library" model="attendance.location">
            <field name="name">Library</field>
            <field name="code">LIBRARY</field>
            <field name="description">Library and study area</field>
            <field name="sequence">20</field>
            <field name="color">4</field>
        </record>

        <record id="location_hall_a" model="attendance.location">
            <field name="name">Hall A</field>
            <field name="code">HALL_A</field>
            <field name="description">Main conference hall</field>
            <field name="sequence">30</field>
            <field name="capacity">100</field>
            <field name="color">5</field>
        </record>

    </data>
</odoo>
