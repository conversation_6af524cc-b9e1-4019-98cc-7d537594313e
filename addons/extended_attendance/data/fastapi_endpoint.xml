<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- User for Extended Attendance API -->
        <record
            id="extended_attendance_api_user"
            model="res.users"
            context="{'no_reset_password': True}"
        >
            <field name="name">Extended Attendance API User</field>
            <field name="login">extended_attendance_api_user</field>
            <field name="groups_id" eval="[(6, 0, [])]" />
        </record>

        <!-- Group for Extended Attendance API -->
        <record id="extended_attendance_api_group" model="res.groups">
            <field name="name">Extended Attendance API Group</field>
            <field name="users" eval="[(4, ref('extended_attendance_api_user'))]" />
            <field name="implied_ids" eval="[(4, ref('fastapi.group_fastapi_endpoint_runner'))]" />
        </record>

        <!-- FastAPI Endpoint Configuration -->
        <record id="fastapi_endpoint_attendance" model="fastapi.endpoint">
            <field name="name">Extended Attendance API</field>
            <field name="description"><![CDATA[
# Extended Attendance Management API

This API provides comprehensive attendance management with:

* **Person Types**: Manage different types of people (employees, students, guests)
* **Locations**: Handle multiple attendance locations
* **Check-in/Check-out**: Record attendance with various identifier types
* **Real-time Tracking**: Live attendance status and occupancy

Supports barcode, RFID, QR codes, and manual entry.
]]></field>
            <field name="app">extended_attendance</field>
            <field name="root_path">/api/attendance</field>
            <field name="user_id" ref="extended_attendance_api_user"/>
        </record>
    </data>
</odoo>
