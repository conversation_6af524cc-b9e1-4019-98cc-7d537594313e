# Copyright 2023 Extended Attendance
# License LGPL-3.0 or later (http://www.gnu.org/licenses/LGPL).

from odoo import api, fields, models
from fastapi import APIRouter

# Import the routers from the controllers
from ..controllers.fastapi_attendance import router as person_types_router
from ..fastapi_endpoints.attendance_api import router as attendance_router

APP_NAME = "extended_attendance"


class FastapiEndpoint(models.Model):
    """Extended Attendance FastAPI Endpoint"""
    
    _inherit = "fastapi.endpoint"

    app: str = fields.Selection(
        selection_add=[(APP_NAME, "Extended Attendance API")],
        ondelete={APP_NAME: "cascade"},
    )

    @api.model
    def _get_fastapi_routers(self):
        """Return the FastAPI routers for the extended attendance app"""
        if self.app == APP_NAME:
            return [
                person_types_router,
                attendance_router,
            ]
        return super()._get_fastapi_routers()
