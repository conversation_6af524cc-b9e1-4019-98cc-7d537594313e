from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, <PERSON>
from typing import Optional
from datetime import datetime

from odoo import fields
from odoo.addons.fastapi.dependencies import odoo_env


# Pydantic models for request/response (auto-generates Swagger docs)
class CheckInRequest(BaseModel):
    """Request model for check-in"""
    person_identifier: str = Field(..., description="Person ID, barcode, RFID, or QR code")
    location_code: str = Field(..., description="Location code")
    device_id: Optional[str] = Field(None, description="Device ID (optional)")
    check_in_time: Optional[datetime] = Field(None, description="Custom check-in time (optional)")

class CheckOutRequest(BaseModel):
    """Request model for check-out"""
    person_identifier: str = Field(..., description="Person ID, barcode, RFID, or QR code")
    location_code: str = Field(..., description="Location code")
    device_id: Optional[str] = Field(None, description="Device ID (optional)")
    check_out_time: Optional[datetime] = Field(None, description="Custom check-out time (optional)")

class AttendanceResponse(BaseModel):
    """Response model for attendance operations"""
    success: bool
    message: str
    data: dict

class PersonResponse(BaseModel):
    """Response model for person data"""
    id: int
    name: str
    person_id: str
    person_type: str
    access_level: str
    active: bool

# Create FastAPI router
router = APIRouter(prefix="/api/attendance", tags=["Attendance"])

@router.post("/check-in", response_model=AttendanceResponse)
def check_in(request: CheckInRequest, env=Depends(odoo_env)):
    """
    Check in a person at a location
    
    This endpoint allows checking in a person using various identifiers:
    - Person ID
    - Barcode
    - RFID tag
    - QR code
    
    Returns attendance record with person and location details.
    """
    try:
        # Convert datetime if provided
        check_in_time = None
        if request.check_in_time:
            check_in_time = fields.Datetime.to_string(request.check_in_time)

        # Call your existing model method
        attendance = env['extended.attendance.record'].create_check_in(
            request.person_identifier, 
            request.location_code, 
            request.device_id, 
            check_in_time
        )

        data = {
            'id': attendance.id,
            'person_name': attendance.person_name,
            'location_name': attendance.location_name,
            'check_in': attendance.check_in.isoformat(),
            'state': attendance.state,
        }

        return AttendanceResponse(
            success=True,
            message=f"Successfully checked in {attendance.person_name} at {attendance.location_name}",
            data=data
        )

    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/check-out", response_model=AttendanceResponse)
def check_out(request: CheckOutRequest, env=Depends(odoo_env)):
    """
    Check out a person from a location
    
    This endpoint allows checking out a person using various identifiers.
    """
    try:
        # Convert datetime if provided
        check_out_time = None
        if request.check_out_time:
            check_out_time = fields.Datetime.to_string(request.check_out_time)

        # Call your existing model method
        attendance = env['extended.attendance.record'].create_check_out(
            request.person_identifier, 
            request.location_code, 
            request.device_id, 
            check_out_time
        )

        data = {
            'id': attendance.id,
            'person_name': attendance.person_name,
            'location_name': attendance.location_name,
            'check_out': attendance.check_out.isoformat() if attendance.check_out else None,
            'state': attendance.state,
        }

        return AttendanceResponse(
            success=True,
            message=f"Successfully checked out {attendance.person_name} from {attendance.location_name}",
            data=data
        )

    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/person/{identifier}", response_model=PersonResponse)
def get_person(identifier: str, env=Depends(odoo_env)):
    """
    Get person details by identifier
    
    Supports searching by:
    - Person ID
    - Barcode
    - RFID tag
    - QR code
    """
    try:
        person = env['extended.attendance.person'].search_by_identifier(identifier)
        
        if not person:
            raise HTTPException(status_code=404, detail="Person not found")

        return PersonResponse(
            id=person.id,
            name=person.name,
            person_id=person.person_id,
            person_type=person.person_type_id.name if person.person_type_id else "Unknown",
            access_level=person.access_level,
            active=person.active
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/locations")
def get_locations(env=Depends(odoo_env)):
    """
    Get all available locations
    """
    try:
        locations = env['attendance.location'].search([('active', '=', True)])
        
        data = []
        for location in locations:
            data.append({
                'id': location.id,
                'name': location.name,
                'code': location.code,
                'capacity': location.capacity,
                'current_occupancy': location.current_occupancy,
                'building': location.building,
                'floor': location.floor,
                'active': location.active
            })

        return {
            'success': True,
            'data': data,
            'count': len(data)
        }

    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
