from fastapi import FastAP<PERSON>
from .attendance_api import router as attendance_router
from ..controllers.fastapi_attendance import router as person_types_router

# Create FastAPI app
app = FastAPI(
    title="Extended Attendance API",
    description="""
    ## Extended Attendance Management System
    
    This API provides endpoints for managing attendance with advanced features:
    
    * **Check-in/Check-out**: Record attendance with multiple identifier types
    * **Person Management**: Search and manage people with different access levels
    * **Location Management**: Handle multiple locations and access control
    * **Real-time Tracking**: Live attendance status and occupancy
    
    ### Supported Identifiers
    - Person ID
    - Barcode
    - RFID tags
    - QR codes
    
    ### Authentication
    Uses Odoo's built-in authentication system.
    """,
    version="1.0.0",
    contact={
        "name": "Extended Attendance Support",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "LGPL-3.0",
        "url": "https://www.gnu.org/licenses/lgpl-3.0.html",
    },
)

# Include routers
app.include_router(attendance_router)
app.include_router(person_types_router)

# Health check endpoint
@app.get("/health")
def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "Extended Attendance API"}
