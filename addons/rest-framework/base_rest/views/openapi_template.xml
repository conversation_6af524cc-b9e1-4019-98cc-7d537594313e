<?xml version="1.0" encoding="utf-8" ?>
<!-- Copyright 2017 ACSONE SA/NV
     License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl). -->
<odoo>
    <template id="openapi" name="OpenAPI">
        <t t-call="web.layout">
            <t t-set="head">
                <meta name="generator" content="Odoo OpenAPI" />
                <meta name="description" content="Odoo OpenAPI UI" />
                <script src="/web/static/lib/owl/owl.js" />
                <link
                    href="/web/static/lib/bootstrap/dist/css/bootstrap.css"
                    rel="stylesheet"
                />
                <link
                    href="/web/static/src/libs/fontawesome/css/font-awesome.css"
                    rel="stylesheet"
                />

                <link
                    rel="stylesheet"
                    href="/base_rest/static/lib/swagger-ui-3.51.1/swagger-ui.css"
                />
                <script
                    type="text/javascript"
                    src="/base_rest/static/lib/swagger-ui-3.51.1/swagger-ui-bundle.js"
                />
                <script
                    type="text/javascript"
                    src="/base_rest/static/lib/swagger-ui-3.51.1/swagger-ui-standalone-preset.js"
                />
                <script type="text/javascript">
                    odoo.session_info = {
                    is_superuser:<t
                    t-esc="json.dumps(request.env.user._is_superuser())"
                />,
                    is_frontend: true,
                    };
                </script>

                <t t-call-assets="web.assets_common" t-js="false" />
                <t t-call-assets="web.assets_frontend" t-js="false" />

                <link
                    href="https://fonts.googleapis.com/css?family=Open+Sans:400,700|Source+Code+Pro:300,600|Titillium+Web:400,600,700"
                    rel="stylesheet"
                />
                <link
                    rel="icon"
                    type="image/png"
                    href="/base_rest/static/lib/swagger-ui-3.51.1/favicon-32x32.png"
                    sizes="32x32"
                />
                <link
                    rel="icon"
                    type="image/png"
                    href="/base_rest/static/lib/swagger-ui-3.51.1/favicon-16x16.png"
                    sizes="16x16"
                />

                <script
                    type="text/javascript"
                    src="/base_rest/static/src/js/components/swagger_ui.js"
                />
            </t>
            <t t-set="head" t-value="head" />
        </t>

        <body>
            <div id="swagger-ui" t-att-data-settings='json.dumps(swagger_settings)' />
        </body>
    </template>
</odoo>
