<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="generator" content="Docutils: https://docutils.sourceforge.io/" />
<title>README.rst</title>
<style type="text/css">

/*
:Author: <PERSON> (<EMAIL>)
:Id: $Id: html4css1.css 9511 2024-01-13 09:50:07Z milde $
:Copyright: This stylesheet has been placed in the public domain.

Default cascading style sheet for the HTML output of Docutils.
Despite the name, some widely supported CSS2 features are used.

See https://docutils.sourceforge.io/docs/howto/html-stylesheets.html for how to
customize this style sheet.
*/

/* used to remove borders from tables and images */
.borderless, table.borderless td, table.borderless th {
  border: 0 }

table.borderless td, table.borderless th {
  /* Override padding for "table.docutils td" with "! important".
     The right padding separates the table cells. */
  padding: 0 0.5em 0 0 ! important }

.first {
  /* Override more specific margin styles with "! important". */
  margin-top: 0 ! important }

.last, .with-subtitle {
  margin-bottom: 0 ! important }

.hidden {
  display: none }

.subscript {
  vertical-align: sub;
  font-size: smaller }

.superscript {
  vertical-align: super;
  font-size: smaller }

a.toc-backref {
  text-decoration: none ;
  color: black }

blockquote.epigraph {
  margin: 2em 5em ; }

dl.docutils dd {
  margin-bottom: 0.5em }

object[type="image/svg+xml"], object[type="application/x-shockwave-flash"] {
  overflow: hidden;
}

/* Uncomment (and remove this text!) to get bold-faced definition list terms
dl.docutils dt {
  font-weight: bold }
*/

div.abstract {
  margin: 2em 5em }

div.abstract p.topic-title {
  font-weight: bold ;
  text-align: center }

div.admonition, div.attention, div.caution, div.danger, div.error,
div.hint, div.important, div.note, div.tip, div.warning {
  margin: 2em ;
  border: medium outset ;
  padding: 1em }

div.admonition p.admonition-title, div.hint p.admonition-title,
div.important p.admonition-title, div.note p.admonition-title,
div.tip p.admonition-title {
  font-weight: bold ;
  font-family: sans-serif }

div.attention p.admonition-title, div.caution p.admonition-title,
div.danger p.admonition-title, div.error p.admonition-title,
div.warning p.admonition-title, .code .error {
  color: red ;
  font-weight: bold ;
  font-family: sans-serif }

/* Uncomment (and remove this text!) to get reduced vertical space in
   compound paragraphs.
div.compound .compound-first, div.compound .compound-middle {
  margin-bottom: 0.5em }

div.compound .compound-last, div.compound .compound-middle {
  margin-top: 0.5em }
*/

div.dedication {
  margin: 2em 5em ;
  text-align: center ;
  font-style: italic }

div.dedication p.topic-title {
  font-weight: bold ;
  font-style: normal }

div.figure {
  margin-left: 2em ;
  margin-right: 2em }

div.footer, div.header {
  clear: both;
  font-size: smaller }

div.line-block {
  display: block ;
  margin-top: 1em ;
  margin-bottom: 1em }

div.line-block div.line-block {
  margin-top: 0 ;
  margin-bottom: 0 ;
  margin-left: 1.5em }

div.sidebar {
  margin: 0 0 0.5em 1em ;
  border: medium outset ;
  padding: 1em ;
  background-color: #ffffee ;
  width: 40% ;
  float: right ;
  clear: right }

div.sidebar p.rubric {
  font-family: sans-serif ;
  font-size: medium }

div.system-messages {
  margin: 5em }

div.system-messages h1 {
  color: red }

div.system-message {
  border: medium outset ;
  padding: 1em }

div.system-message p.system-message-title {
  color: red ;
  font-weight: bold }

div.topic {
  margin: 2em }

h1.section-subtitle, h2.section-subtitle, h3.section-subtitle,
h4.section-subtitle, h5.section-subtitle, h6.section-subtitle {
  margin-top: 0.4em }

h1.title {
  text-align: center }

h2.subtitle {
  text-align: center }

hr.docutils {
  width: 75% }

img.align-left, .figure.align-left, object.align-left, table.align-left {
  clear: left ;
  float: left ;
  margin-right: 1em }

img.align-right, .figure.align-right, object.align-right, table.align-right {
  clear: right ;
  float: right ;
  margin-left: 1em }

img.align-center, .figure.align-center, object.align-center {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

table.align-center {
  margin-left: auto;
  margin-right: auto;
}

.align-left {
  text-align: left }

.align-center {
  clear: both ;
  text-align: center }

.align-right {
  text-align: right }

/* reset inner alignment in figures */
div.align-right {
  text-align: inherit }

/* div.align-center * { */
/*   text-align: left } */

.align-top    {
  vertical-align: top }

.align-middle {
  vertical-align: middle }

.align-bottom {
  vertical-align: bottom }

ol.simple, ul.simple {
  margin-bottom: 1em }

ol.arabic {
  list-style: decimal }

ol.loweralpha {
  list-style: lower-alpha }

ol.upperalpha {
  list-style: upper-alpha }

ol.lowerroman {
  list-style: lower-roman }

ol.upperroman {
  list-style: upper-roman }

p.attribution {
  text-align: right ;
  margin-left: 50% }

p.caption {
  font-style: italic }

p.credits {
  font-style: italic ;
  font-size: smaller }

p.label {
  white-space: nowrap }

p.rubric {
  font-weight: bold ;
  font-size: larger ;
  color: maroon ;
  text-align: center }

p.sidebar-title {
  font-family: sans-serif ;
  font-weight: bold ;
  font-size: larger }

p.sidebar-subtitle {
  font-family: sans-serif ;
  font-weight: bold }

p.topic-title {
  font-weight: bold }

pre.address {
  margin-bottom: 0 ;
  margin-top: 0 ;
  font: inherit }

pre.literal-block, pre.doctest-block, pre.math, pre.code {
  margin-left: 2em ;
  margin-right: 2em }

pre.code .ln { color: gray; } /* line numbers */
pre.code, code { background-color: #eeeeee }
pre.code .comment, code .comment { color: #5C6576 }
pre.code .keyword, code .keyword { color: #3B0D06; font-weight: bold }
pre.code .literal.string, code .literal.string { color: #0C5404 }
pre.code .name.builtin, code .name.builtin { color: #352B84 }
pre.code .deleted, code .deleted { background-color: #DEB0A1}
pre.code .inserted, code .inserted { background-color: #A3D289}

span.classifier {
  font-family: sans-serif ;
  font-style: oblique }

span.classifier-delimiter {
  font-family: sans-serif ;
  font-weight: bold }

span.interpreted {
  font-family: sans-serif }

span.option {
  white-space: nowrap }

span.pre {
  white-space: pre }

span.problematic, pre.problematic {
  color: red }

span.section-subtitle {
  /* font-size relative to parent (h1..h6 element) */
  font-size: 80% }

table.citation {
  border-left: solid 1px gray;
  margin-left: 1px }

table.docinfo {
  margin: 2em 4em }

table.docutils {
  margin-top: 0.5em ;
  margin-bottom: 0.5em }

table.footnote {
  border-left: solid 1px black;
  margin-left: 1px }

table.docutils td, table.docutils th,
table.docinfo td, table.docinfo th {
  padding-left: 0.5em ;
  padding-right: 0.5em ;
  vertical-align: top }

table.docutils th.field-name, table.docinfo th.docinfo-name {
  font-weight: bold ;
  text-align: left ;
  white-space: nowrap ;
  padding-left: 0 }

/* "booktabs" style (no vertical lines) */
table.docutils.booktabs {
  border: 0px;
  border-top: 2px solid;
  border-bottom: 2px solid;
  border-collapse: collapse;
}
table.docutils.booktabs * {
  border: 0px;
}
table.docutils.booktabs th {
  border-bottom: thin solid;
  text-align: left;
}

h1 tt.docutils, h2 tt.docutils, h3 tt.docutils,
h4 tt.docutils, h5 tt.docutils, h6 tt.docutils {
  font-size: 100% }

ul.auto-toc {
  list-style-type: none }

</style>
</head>
<body>
<div class="document">


<a class="reference external image-reference" href="https://odoo-community.org/get-involved?utm_source=readme">
<img alt="Odoo Community Association" src="https://odoo-community.org/readme-banner-image" />
</a>
<div class="section" id="base-rest">
<h1>Base Rest</h1>
<!-- !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
!! This file is generated by oca-gen-addon-readme !!
!! changes will be overwritten.                   !!
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
!! source digest: sha256:198b97806a0a8c480e6c8f26f3ce3199cba9978f8d152ecf93c80091323d1805
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! -->
<p><a class="reference external image-reference" href="https://odoo-community.org/page/development-status"><img alt="Beta" src="https://img.shields.io/badge/maturity-Beta-yellow.png" /></a> <a class="reference external image-reference" href="http://www.gnu.org/licenses/lgpl-3.0-standalone.html"><img alt="License: LGPL-3" src="https://img.shields.io/badge/license-LGPL--3-blue.png" /></a> <a class="reference external image-reference" href="https://github.com/OCA/rest-framework/tree/18.0/base_rest"><img alt="OCA/rest-framework" src="https://img.shields.io/badge/github-OCA%2Frest--framework-lightgray.png?logo=github" /></a> <a class="reference external image-reference" href="https://translation.odoo-community.org/projects/rest-framework-18-0/rest-framework-18-0-base_rest"><img alt="Translate me on Weblate" src="https://img.shields.io/badge/weblate-Translate%20me-F47D42.png" /></a> <a class="reference external image-reference" href="https://runboat.odoo-community.org/builds?repo=OCA/rest-framework&amp;target_branch=18.0"><img alt="Try me on Runboat" src="https://img.shields.io/badge/runboat-Try%20me-875A7B.png" /></a></p>
<p>This addon is deprecated and not fully supported anymore from Odoo 16.
Please migrate to the FastAPI migration module. See
<a class="reference external" href="https://github.com/OCA/rest-framework/pull/291">https://github.com/OCA/rest-framework/pull/291</a>.</p>
<p>This addon provides the basis to develop high level REST APIs for Odoo.</p>
<p>As Odoo becomes one of the central pieces of enterprise IT systems, it
often becomes necessary to set up specialized service interfaces, so
existing systems can interact with Odoo.</p>
<p>While the XML-RPC interface of Odoo comes handy in such situations, it
requires a deep understanding of Odoo’s internal data model. When used
extensively, it creates a strong coupling between Odoo internals and
client systems, therefore increasing maintenance costs.</p>
<p>Once developed, an <a class="reference external" href="https://spec.openapis.org/oas/v3.0.3">OpenApi</a>
documentation is generated from the source code and available via a
<a class="reference external" href="https://swagger.io/tools/swagger-ui/">Swagger UI</a> served by your
odoo server at <a class="reference external" href="https://my_odoo_server/api-docs">https://my_odoo_server/api-docs</a>.</p>
<p><strong>Table of contents</strong></p>
<div class="contents local topic" id="contents">
<ul class="simple">
<li><a class="reference internal" href="#configuration" id="toc-entry-1">Configuration</a></li>
<li><a class="reference internal" href="#usage" id="toc-entry-2">Usage</a></li>
<li><a class="reference internal" href="#known-issues-roadmap" id="toc-entry-3">Known issues / Roadmap</a></li>
<li><a class="reference internal" href="#changelog" id="toc-entry-4">Changelog</a><ul>
<li><a class="reference internal" href="#section-1" id="toc-entry-5">16.0.1.0.2 (2023-10-07)</a></li>
<li><a class="reference internal" href="#section-2" id="toc-entry-6">12.0.2.0.1</a></li>
<li><a class="reference internal" href="#section-3" id="toc-entry-7">12.0.2.0.0</a></li>
<li><a class="reference internal" href="#section-4" id="toc-entry-8">12.0.1.0.1</a></li>
<li><a class="reference internal" href="#section-5" id="toc-entry-9">12.0.1.0.0</a></li>
</ul>
</li>
<li><a class="reference internal" href="#bug-tracker" id="toc-entry-10">Bug Tracker</a></li>
<li><a class="reference internal" href="#credits" id="toc-entry-11">Credits</a><ul>
<li><a class="reference internal" href="#authors" id="toc-entry-12">Authors</a></li>
<li><a class="reference internal" href="#contributors" id="toc-entry-13">Contributors</a></li>
<li><a class="reference internal" href="#maintainers" id="toc-entry-14">Maintainers</a></li>
</ul>
</li>
</ul>
</div>
<div class="section" id="configuration">
<h2><a class="toc-backref" href="#toc-entry-1">Configuration</a></h2>
<p>If an error occurs when calling a method of a service (ie missing
parameter, ..) the system returns only a general description of the
problem without details. This is done on purpose to ensure maximum
opacity on implementation details and therefore lower security issue.</p>
<p>This restriction can be problematic when the services are accessed by an
external system in development. To know the details of an error it is
indeed necessary to have access to the log of the server. It is not
always possible to provide this kind of access. That’s why you can
configure the server to run these services in development mode.</p>
<p>To run the REST API in development mode you must add a new section
‘**[base_rest]**’ with the option ‘**dev_mode=True**’ in the server
config file.</p>
<pre class="code cfg literal-block">
<span class="k">[base_rest]</span><span class="w">
</span><span class="na">dev_mode</span><span class="o">=</span><span class="s">True</span>
</pre>
<p>When the REST API runs in development mode, the original description and
a stack trace is returned in case of error. <strong>Be careful to not use this
mode in production</strong>.</p>
</div>
<div class="section" id="usage">
<h2><a class="toc-backref" href="#toc-entry-2">Usage</a></h2>
<p>To add your own REST service you must provides at least 2 classes.</p>
<ul class="simple">
<li>A Component providing the business logic of your service,</li>
<li>A Controller to register your service.</li>
</ul>
<p>The business logic of your service must be implemented into a component
(<tt class="docutils literal">odoo.addons.component.core.Component</tt>) that inherit from
‘base.rest.service’</p>
<p>Initially, base_rest expose by default all public methods defined in a
service. The conventions for accessing methods via HTTP were as follows:</p>
<ul class="simple">
<li>The method <tt class="docutils literal">def get(self, _id)</tt> if defined, is accessible via HTTP
GET routes <tt class="docutils literal"><span class="pre">&lt;string:_service_name&gt;/&lt;int:_id&gt;</span></tt> and
<tt class="docutils literal"><span class="pre">&lt;string:_service_name&gt;/&lt;int:_id&gt;/get</span></tt>.</li>
<li>The method <tt class="docutils literal">def search(self, **params)</tt> if defined, is accessible
via the HTTP GET routes <tt class="docutils literal">&lt;string:_service_name&gt;/</tt> and
<tt class="docutils literal"><span class="pre">&lt;string:_service_name&gt;/search</span></tt>.</li>
<li>The method <tt class="docutils literal">def delete(self, _id)</tt> if defined, is accessible via the
HTTP DELETE route <tt class="docutils literal"><span class="pre">&lt;string:_service_name&gt;/&lt;int:_id&gt;</span></tt>.</li>
<li>The <tt class="docutils literal">def update(self, _id, **params)</tt> method, if defined, is
accessible via the HTTP PUT route
<tt class="docutils literal"><span class="pre">&lt;string:_service_name&gt;/&lt;int:_id&gt;</span></tt>.</li>
<li>Other methods are only accessible via HTTP POST routes
<tt class="docutils literal">&lt;string:_service_name&gt;</tt> or
<tt class="docutils literal"><span class="pre">&lt;string:_service_name&gt;/&lt;string:method_name&gt;</span></tt> or
<tt class="docutils literal"><span class="pre">&lt;string:_service_name&gt;/&lt;int:_id&gt;</span></tt> or
<tt class="docutils literal"><span class="pre">&lt;string:_service_name&gt;/&lt;int:_id&gt;/&lt;string:method_name&gt;</span></tt></li>
</ul>
<pre class="code python literal-block">
<span class="kn">from</span><span class="w"> </span><span class="nn">odoo.addons.component.core</span><span class="w"> </span><span class="kn">import</span> <span class="n">Component</span><span class="w">


</span><span class="k">class</span><span class="w"> </span><span class="nc">PingService</span><span class="p">(</span><span class="n">Component</span><span class="p">):</span><span class="w">
</span>    <span class="n">_inherit</span> <span class="o">=</span> <span class="s1">'base.rest.service'</span><span class="w">
</span>    <span class="n">_name</span> <span class="o">=</span> <span class="s1">'ping.service'</span><span class="w">
</span>    <span class="n">_usage</span> <span class="o">=</span> <span class="s1">'ping'</span><span class="w">
</span>    <span class="n">_collection</span> <span class="o">=</span> <span class="s1">'my_module.services'</span><span class="w">


</span>    <span class="c1"># The following method are 'public' and can be called from the controller.</span><span class="w">
</span>    <span class="k">def</span><span class="w"> </span><span class="nf">get</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">_id</span><span class="p">,</span> <span class="n">message</span><span class="p">):</span><span class="w">
</span>        <span class="k">return</span> <span class="p">{</span><span class="w">
</span>            <span class="s1">'response'</span><span class="p">:</span> <span class="s1">'Get called with message '</span> <span class="o">+</span> <span class="n">message</span><span class="p">}</span><span class="w">

</span>    <span class="k">def</span><span class="w"> </span><span class="nf">search</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">message</span><span class="p">):</span><span class="w">
</span>        <span class="k">return</span> <span class="p">{</span><span class="w">
</span>            <span class="s1">'response'</span><span class="p">:</span> <span class="s1">'Search called search with message '</span> <span class="o">+</span> <span class="n">message</span><span class="p">}</span><span class="w">

</span>    <span class="k">def</span><span class="w"> </span><span class="nf">update</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">_id</span><span class="p">,</span> <span class="n">message</span><span class="p">):</span><span class="w">
</span>        <span class="k">return</span> <span class="p">{</span><span class="s1">'response'</span><span class="p">:</span> <span class="s1">'PUT called with message '</span> <span class="o">+</span> <span class="n">message</span><span class="p">}</span><span class="w">

</span>    <span class="c1"># pylint:disable=method-required-super</span><span class="w">
</span>    <span class="k">def</span><span class="w"> </span><span class="nf">create</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="o">**</span><span class="n">params</span><span class="p">):</span><span class="w">
</span>        <span class="k">return</span> <span class="p">{</span><span class="s1">'response'</span><span class="p">:</span> <span class="s1">'POST called with message '</span> <span class="o">+</span> <span class="n">params</span><span class="p">[</span><span class="s1">'message'</span><span class="p">]}</span><span class="w">

</span>    <span class="k">def</span><span class="w"> </span><span class="nf">delete</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">_id</span><span class="p">):</span><span class="w">
</span>        <span class="k">return</span> <span class="p">{</span><span class="s1">'response'</span><span class="p">:</span> <span class="s1">'DELETE called with id </span><span class="si">%s</span><span class="s1"> '</span> <span class="o">%</span> <span class="n">_id</span><span class="p">}</span><span class="w">

</span>    <span class="c1"># Validator</span><span class="w">
</span>    <span class="k">def</span><span class="w"> </span><span class="nf">_validator_search</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span><span class="w">
</span>        <span class="k">return</span> <span class="p">{</span><span class="s1">'message'</span><span class="p">:</span> <span class="p">{</span><span class="s1">'type'</span><span class="p">:</span> <span class="s1">'string'</span><span class="p">}}</span><span class="w">

</span>    <span class="c1"># Validator</span><span class="w">
</span>    <span class="k">def</span><span class="w"> </span><span class="nf">_validator_get</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span><span class="w">
</span>        <span class="c1"># no parameters by default</span><span class="w">
</span>        <span class="k">return</span> <span class="p">{}</span><span class="w">

</span>    <span class="k">def</span><span class="w"> </span><span class="nf">_validator_update</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span><span class="w">
</span>        <span class="k">return</span> <span class="p">{</span><span class="s1">'message'</span><span class="p">:</span> <span class="p">{</span><span class="s1">'type'</span><span class="p">:</span> <span class="s1">'string'</span><span class="p">}}</span><span class="w">

</span>    <span class="k">def</span><span class="w"> </span><span class="nf">_validator_create</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span><span class="w">
</span>        <span class="k">return</span> <span class="p">{</span><span class="s1">'message'</span><span class="p">:</span> <span class="p">{</span><span class="s1">'type'</span><span class="p">:</span> <span class="s1">'string'</span><span class="p">}}</span>
</pre>
<p>Once you have implemented your services (ping, …), you must tell to
Odoo how to access to these services. This process is done by
implementing a controller that inherits from
<tt class="docutils literal">odoo.addons.base_rest.controllers.main.RestController</tt></p>
<pre class="code python literal-block">
<span class="kn">from</span><span class="w"> </span><span class="nn">odoo.addons.base_rest.controllers</span><span class="w"> </span><span class="kn">import</span> <span class="n">main</span><span class="w">

</span><span class="k">class</span><span class="w"> </span><span class="nc">MyRestController</span><span class="p">(</span><span class="n">main</span><span class="o">.</span><span class="n">RestController</span><span class="p">):</span><span class="w">
</span>    <span class="n">_root_path</span> <span class="o">=</span> <span class="s1">'/my_services_api/'</span><span class="w">
</span>    <span class="n">_collection_name</span> <span class="o">=</span> <span class="n">my_module</span><span class="o">.</span><span class="n">services</span>
</pre>
<p>In your controller, _’root_path’ is used to specify the root of the
path to access to your services and ‘_collection_name’ is the name of
the collection providing the business logic for the requested service/</p>
<p>By inheriting from <tt class="docutils literal">RestController</tt> the following routes will be
registered to access to your services</p>
<pre class="code python literal-block">
<span class="nd">&#64;route</span><span class="p">([</span><span class="w">
</span>    <span class="n">ROOT_PATH</span> <span class="o">+</span> <span class="s1">'&lt;string:_service_name&gt;'</span><span class="p">,</span><span class="w">
</span>    <span class="n">ROOT_PATH</span> <span class="o">+</span> <span class="s1">'&lt;string:_service_name&gt;/search'</span><span class="p">,</span><span class="w">
</span>    <span class="n">ROOT_PATH</span> <span class="o">+</span> <span class="s1">'&lt;string:_service_name&gt;/&lt;int:_id&gt;'</span><span class="p">,</span><span class="w">
</span>    <span class="n">ROOT_PATH</span> <span class="o">+</span> <span class="s1">'&lt;string:_service_name&gt;/&lt;int:_id&gt;/get'</span><span class="w">
</span><span class="p">],</span> <span class="n">methods</span><span class="o">=</span><span class="p">[</span><span class="s1">'GET'</span><span class="p">],</span> <span class="n">auth</span><span class="o">=</span><span class="s2">&quot;user&quot;</span><span class="p">,</span> <span class="n">csrf</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span><span class="w">
</span><span class="k">def</span><span class="w"> </span><span class="nf">get</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">_service_name</span><span class="p">,</span> <span class="n">_id</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="o">**</span><span class="n">params</span><span class="p">):</span><span class="w">
</span>    <span class="n">method_name</span> <span class="o">=</span> <span class="s1">'get'</span> <span class="k">if</span> <span class="n">_id</span> <span class="k">else</span> <span class="s1">'search'</span><span class="w">
</span>    <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_process_method</span><span class="p">(</span><span class="n">_service_name</span><span class="p">,</span> <span class="n">method_name</span><span class="p">,</span> <span class="n">_id</span><span class="p">,</span> <span class="n">params</span><span class="p">)</span><span class="w">

</span><span class="nd">&#64;route</span><span class="p">([</span><span class="w">
</span>    <span class="n">ROOT_PATH</span> <span class="o">+</span> <span class="s1">'&lt;string:_service_name&gt;'</span><span class="p">,</span><span class="w">
</span>    <span class="n">ROOT_PATH</span> <span class="o">+</span> <span class="s1">'&lt;string:_service_name&gt;/&lt;string:method_name&gt;'</span><span class="p">,</span><span class="w">
</span>    <span class="n">ROOT_PATH</span> <span class="o">+</span> <span class="s1">'&lt;string:_service_name&gt;/&lt;int:_id&gt;'</span><span class="p">,</span><span class="w">
</span>    <span class="n">ROOT_PATH</span> <span class="o">+</span> <span class="s1">'&lt;string:_service_name&gt;/&lt;int:_id&gt;/&lt;string:method_name&gt;'</span><span class="w">
</span><span class="p">],</span> <span class="n">methods</span><span class="o">=</span><span class="p">[</span><span class="s1">'POST'</span><span class="p">],</span> <span class="n">auth</span><span class="o">=</span><span class="s2">&quot;user&quot;</span><span class="p">,</span> <span class="n">csrf</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span><span class="w">
</span><span class="k">def</span><span class="w"> </span><span class="nf">modify</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">_service_name</span><span class="p">,</span> <span class="n">_id</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">method_name</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="o">**</span><span class="n">params</span><span class="p">):</span><span class="w">
</span>    <span class="k">if</span> <span class="ow">not</span> <span class="n">method_name</span><span class="p">:</span><span class="w">
</span>        <span class="n">method_name</span> <span class="o">=</span> <span class="s1">'update'</span> <span class="k">if</span> <span class="n">_id</span> <span class="k">else</span> <span class="s1">'create'</span><span class="w">
</span>    <span class="k">if</span> <span class="n">method_name</span> <span class="o">==</span> <span class="s1">'get'</span><span class="p">:</span><span class="w">
</span>        <span class="n">_logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="s2">&quot;HTTP POST with method name 'get' is not allowed. &quot;</span><span class="w">
</span>                      <span class="s2">&quot;(service name: </span><span class="si">%s</span><span class="s2">)&quot;</span><span class="p">,</span> <span class="n">_service_name</span><span class="p">)</span><span class="w">
</span>        <span class="k">raise</span> <span class="n">BadRequest</span><span class="p">()</span><span class="w">
</span>    <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_process_method</span><span class="p">(</span><span class="n">_service_name</span><span class="p">,</span> <span class="n">method_name</span><span class="p">,</span> <span class="n">_id</span><span class="p">,</span> <span class="n">params</span><span class="p">)</span><span class="w">

</span><span class="nd">&#64;route</span><span class="p">([</span><span class="w">
</span>    <span class="n">ROOT_PATH</span> <span class="o">+</span> <span class="s1">'&lt;string:_service_name&gt;/&lt;int:_id&gt;'</span><span class="p">,</span><span class="w">
</span><span class="p">],</span> <span class="n">methods</span><span class="o">=</span><span class="p">[</span><span class="s1">'PUT'</span><span class="p">],</span> <span class="n">auth</span><span class="o">=</span><span class="s2">&quot;user&quot;</span><span class="p">,</span> <span class="n">csrf</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span><span class="w">
</span><span class="k">def</span><span class="w"> </span><span class="nf">update</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">_service_name</span><span class="p">,</span> <span class="n">_id</span><span class="p">,</span> <span class="o">**</span><span class="n">params</span><span class="p">):</span><span class="w">
</span>    <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_process_method</span><span class="p">(</span><span class="n">_service_name</span><span class="p">,</span> <span class="s1">'update'</span><span class="p">,</span> <span class="n">_id</span><span class="p">,</span> <span class="n">params</span><span class="p">)</span><span class="w">

</span><span class="nd">&#64;route</span><span class="p">([</span><span class="w">
</span>    <span class="n">ROOT_PATH</span> <span class="o">+</span> <span class="s1">'&lt;string:_service_name&gt;/&lt;int:_id&gt;'</span><span class="p">,</span><span class="w">
</span><span class="p">],</span> <span class="n">methods</span><span class="o">=</span><span class="p">[</span><span class="s1">'DELETE'</span><span class="p">],</span> <span class="n">auth</span><span class="o">=</span><span class="s2">&quot;user&quot;</span><span class="p">,</span> <span class="n">csrf</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span><span class="w">
</span><span class="k">def</span><span class="w"> </span><span class="nf">delete</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">_service_name</span><span class="p">,</span> <span class="n">_id</span><span class="p">):</span><span class="w">
</span>    <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_process_method</span><span class="p">(</span><span class="n">_service_name</span><span class="p">,</span> <span class="s1">'delete'</span><span class="p">,</span> <span class="n">_id</span><span class="p">)</span>
</pre>
<p>As result an HTTP GET call to ‘<a class="reference external" href="http://my_odoo/my_services_api/ping">http://my_odoo/my_services_api/ping</a>’ will
be dispatched to the method <tt class="docutils literal">PingService.search</tt></p>
<p>In addition to easily exposing your methods, the module allows you to
define data schemas to which the exchanged data must conform. These
schemas are defined on the basis of <a class="reference external" href="https://docs.python-cerberus.org/en/stable/">Cerberus
schemas</a> and associated
to the methods using the following naming convention. For a method
`my_method`:</p>
<ul class="simple">
<li><tt class="docutils literal">def _validator_my_method(self):</tt> will be called to get the schema
required to validate the input parameters.</li>
<li><tt class="docutils literal">def _validator_return_my_method(self):</tt> if defined, will be called
to get the schema used to validate the response.</li>
</ul>
<p>In order to offer even more flexibility, a new API has been developed.</p>
<p>This new API replaces the implicit approach used to expose a service by
the use of a python decorator to explicitly mark a method as being
available via the REST API: <tt class="docutils literal">odoo.addons.base_rest.restapi.method</tt>.</p>
<pre class="code python literal-block">
<span class="k">class</span><span class="w"> </span><span class="nc">PartnerNewApiService</span><span class="p">(</span><span class="n">Component</span><span class="p">):</span><span class="w">
</span>    <span class="n">_inherit</span> <span class="o">=</span> <span class="s2">&quot;base.rest.service&quot;</span><span class="w">
</span>    <span class="n">_name</span> <span class="o">=</span> <span class="s2">&quot;partner.new_api.service&quot;</span><span class="w">
</span>    <span class="n">_usage</span> <span class="o">=</span> <span class="s2">&quot;partner&quot;</span><span class="w">
</span>    <span class="n">_collection</span> <span class="o">=</span> <span class="s2">&quot;base.rest.demo.new_api.services&quot;</span><span class="w">
</span>    <span class="n">_description</span> <span class="o">=</span> <span class="s2">&quot;&quot;&quot;
        Partner New API Services
        Services developed with the new api provided by base_rest
    &quot;&quot;&quot;</span><span class="w">

</span>    <span class="nd">&#64;restapi</span><span class="o">.</span><span class="n">method</span><span class="p">(</span><span class="w">
</span>        <span class="p">[([</span><span class="s2">&quot;/&lt;int:id&gt;/get&quot;</span><span class="p">,</span> <span class="s2">&quot;/&lt;int:id&gt;&quot;</span><span class="p">],</span> <span class="s2">&quot;GET&quot;</span><span class="p">)],</span><span class="w">
</span>        <span class="n">output_param</span><span class="o">=</span><span class="n">restapi</span><span class="o">.</span><span class="n">CerberusValidator</span><span class="p">(</span><span class="s2">&quot;_get_partner_schema&quot;</span><span class="p">),</span><span class="w">
</span>        <span class="n">auth</span><span class="o">=</span><span class="s2">&quot;public&quot;</span><span class="p">,</span><span class="w">
</span>    <span class="p">)</span><span class="w">
</span>    <span class="k">def</span><span class="w"> </span><span class="nf">get</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">_id</span><span class="p">):</span><span class="w">
</span>        <span class="k">return</span> <span class="p">{</span><span class="s2">&quot;name&quot;</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">env</span><span class="p">[</span><span class="s2">&quot;res.partner&quot;</span><span class="p">]</span><span class="o">.</span><span class="n">browse</span><span class="p">(</span><span class="n">_id</span><span class="p">)</span><span class="o">.</span><span class="n">name</span><span class="p">}</span><span class="w">

</span>    <span class="k">def</span><span class="w"> </span><span class="nf">_get_partner_schema</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span><span class="w">
</span>        <span class="k">return</span> <span class="p">{</span><span class="w">
</span>            <span class="s2">&quot;name&quot;</span><span class="p">:</span> <span class="p">{</span><span class="s2">&quot;type&quot;</span><span class="p">:</span> <span class="s2">&quot;string&quot;</span><span class="p">,</span> <span class="s2">&quot;required&quot;</span><span class="p">:</span> <span class="kc">True</span><span class="p">}</span><span class="w">
</span>        <span class="p">}</span><span class="w">

</span>    <span class="nd">&#64;restapi</span><span class="o">.</span><span class="n">method</span><span class="p">(</span><span class="w">
</span>        <span class="p">[([</span><span class="s2">&quot;/list&quot;</span><span class="p">,</span> <span class="s2">&quot;/&quot;</span><span class="p">],</span> <span class="s2">&quot;GET&quot;</span><span class="p">)],</span><span class="w">
</span>        <span class="n">output_param</span><span class="o">=</span><span class="n">restapi</span><span class="o">.</span><span class="n">CerberusListValidator</span><span class="p">(</span><span class="s2">&quot;_get_partner_schema&quot;</span><span class="p">),</span><span class="w">
</span>        <span class="n">auth</span><span class="o">=</span><span class="s2">&quot;public&quot;</span><span class="p">,</span><span class="w">
</span>    <span class="p">)</span><span class="w">
</span>    <span class="k">def</span><span class="w"> </span><span class="nf">list</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span><span class="w">
</span>        <span class="n">partners</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">env</span><span class="p">[</span><span class="s2">&quot;res.partner&quot;</span><span class="p">]</span><span class="o">.</span><span class="n">search</span><span class="p">([])</span><span class="w">
</span>        <span class="k">return</span> <span class="p">[{</span><span class="s2">&quot;name&quot;</span><span class="p">:</span> <span class="n">p</span><span class="o">.</span><span class="n">name</span><span class="p">}</span> <span class="k">for</span> <span class="n">p</span> <span class="ow">in</span> <span class="n">partners</span><span class="p">]</span>
</pre>
<p>Thanks to this new api, you are now free to specify your own routes but
also to use other object types as parameter or response to your methods.
For example, base_rest_datamodel allows you to use Datamodel object
instance into your services.</p>
<pre class="code python literal-block">
<span class="kn">from</span><span class="w"> </span><span class="nn">marshmallow</span><span class="w"> </span><span class="kn">import</span> <span class="n">fields</span><span class="w">

</span><span class="kn">from</span><span class="w"> </span><span class="nn">odoo.addons.base_rest</span><span class="w"> </span><span class="kn">import</span> <span class="n">restapi</span><span class="w">
</span><span class="kn">from</span><span class="w"> </span><span class="nn">odoo.addons.component.core</span><span class="w"> </span><span class="kn">import</span> <span class="n">Component</span><span class="w">
</span><span class="kn">from</span><span class="w"> </span><span class="nn">odoo.addons.datamodel.core</span><span class="w"> </span><span class="kn">import</span> <span class="n">Datamodel</span><span class="w">


</span><span class="k">class</span><span class="w"> </span><span class="nc">PartnerSearchParam</span><span class="p">(</span><span class="n">Datamodel</span><span class="p">):</span><span class="w">
</span>    <span class="n">_name</span> <span class="o">=</span> <span class="s2">&quot;partner.search.param&quot;</span><span class="w">

</span>    <span class="nb">id</span> <span class="o">=</span> <span class="n">fields</span><span class="o">.</span><span class="n">Integer</span><span class="p">(</span><span class="n">required</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span> <span class="n">allow_none</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span><span class="w">
</span>    <span class="n">name</span> <span class="o">=</span> <span class="n">fields</span><span class="o">.</span><span class="n">String</span><span class="p">(</span><span class="n">required</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span> <span class="n">allow_none</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span><span class="w">


</span><span class="k">class</span><span class="w"> </span><span class="nc">PartnerShortInfo</span><span class="p">(</span><span class="n">Datamodel</span><span class="p">):</span><span class="w">
</span>    <span class="n">_name</span> <span class="o">=</span> <span class="s2">&quot;partner.short.info&quot;</span><span class="w">

</span>    <span class="nb">id</span> <span class="o">=</span> <span class="n">fields</span><span class="o">.</span><span class="n">Integer</span><span class="p">(</span><span class="n">required</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">allow_none</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span><span class="w">
</span>    <span class="n">name</span> <span class="o">=</span> <span class="n">fields</span><span class="o">.</span><span class="n">String</span><span class="p">(</span><span class="n">required</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">allow_none</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span><span class="w">


</span><span class="k">class</span><span class="w"> </span><span class="nc">PartnerNewApiService</span><span class="p">(</span><span class="n">Component</span><span class="p">):</span><span class="w">
</span>    <span class="n">_inherit</span> <span class="o">=</span> <span class="s2">&quot;base.rest.service&quot;</span><span class="w">
</span>    <span class="n">_name</span> <span class="o">=</span> <span class="s2">&quot;partner.new_api.service&quot;</span><span class="w">
</span>    <span class="n">_usage</span> <span class="o">=</span> <span class="s2">&quot;partner&quot;</span><span class="w">
</span>    <span class="n">_collection</span> <span class="o">=</span> <span class="s2">&quot;base.rest.demo.new_api.services&quot;</span><span class="w">
</span>    <span class="n">_description</span> <span class="o">=</span> <span class="s2">&quot;&quot;&quot;
        Partner New API Services
        Services developed with the new api provided by base_rest
    &quot;&quot;&quot;</span><span class="w">

</span>    <span class="nd">&#64;restapi</span><span class="o">.</span><span class="n">method</span><span class="p">(</span><span class="w">
</span>        <span class="p">[([</span><span class="s2">&quot;/&quot;</span><span class="p">,</span> <span class="s2">&quot;/search&quot;</span><span class="p">],</span> <span class="s2">&quot;GET&quot;</span><span class="p">)],</span><span class="w">
</span>        <span class="n">input_param</span><span class="o">=</span><span class="n">restapi</span><span class="o">.</span><span class="n">Datamodel</span><span class="p">(</span><span class="s2">&quot;partner.search.param&quot;</span><span class="p">),</span><span class="w">
</span>        <span class="n">output_param</span><span class="o">=</span><span class="n">restapi</span><span class="o">.</span><span class="n">Datamodel</span><span class="p">(</span><span class="s2">&quot;partner.short.info&quot;</span><span class="p">,</span> <span class="n">is_list</span><span class="o">=</span><span class="kc">True</span><span class="p">),</span><span class="w">
</span>        <span class="n">auth</span><span class="o">=</span><span class="s2">&quot;public&quot;</span><span class="p">,</span><span class="w">
</span>    <span class="p">)</span><span class="w">
</span>    <span class="k">def</span><span class="w"> </span><span class="nf">search</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">partner_search_param</span><span class="p">):</span><span class="w">
        </span><span class="sd">&quot;&quot;&quot;
        Search for partners
        :param partner_search_param: An instance of partner.search.param
        :return: List of partner.short.info
        &quot;&quot;&quot;</span><span class="w">
</span>        <span class="n">domain</span> <span class="o">=</span> <span class="p">[]</span><span class="w">
</span>        <span class="k">if</span> <span class="n">partner_search_param</span><span class="o">.</span><span class="n">name</span><span class="p">:</span><span class="w">
</span>            <span class="n">domain</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="s2">&quot;name&quot;</span><span class="p">,</span> <span class="s2">&quot;like&quot;</span><span class="p">,</span> <span class="n">partner_search_param</span><span class="o">.</span><span class="n">name</span><span class="p">))</span><span class="w">
</span>        <span class="k">if</span> <span class="n">partner_search_param</span><span class="o">.</span><span class="n">id</span><span class="p">:</span><span class="w">
</span>            <span class="n">domain</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="s2">&quot;id&quot;</span><span class="p">,</span> <span class="s2">&quot;=&quot;</span><span class="p">,</span> <span class="n">partner_search_param</span><span class="o">.</span><span class="n">id</span><span class="p">))</span><span class="w">
</span>        <span class="n">res</span> <span class="o">=</span> <span class="p">[]</span><span class="w">
</span>        <span class="n">PartnerShortInfo</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">env</span><span class="o">.</span><span class="n">datamodels</span><span class="p">[</span><span class="s2">&quot;partner.short.info&quot;</span><span class="p">]</span><span class="w">
</span>        <span class="k">for</span> <span class="n">p</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">env</span><span class="p">[</span><span class="s2">&quot;res.partner&quot;</span><span class="p">]</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="n">domain</span><span class="p">):</span><span class="w">
</span>            <span class="n">res</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">PartnerShortInfo</span><span class="p">(</span><span class="nb">id</span><span class="o">=</span><span class="n">p</span><span class="o">.</span><span class="n">id</span><span class="p">,</span> <span class="n">name</span><span class="o">=</span><span class="n">p</span><span class="o">.</span><span class="n">name</span><span class="p">))</span><span class="w">
</span>        <span class="k">return</span> <span class="n">res</span>
</pre>
<p>The BaseRestServiceContextProvider provides context for your services,
including authenticated_partner_id. You are free to redefine the method
get_authenticated_partner_id() to pass the authenticated_partner_id
based on the authentication mechanism of your choice. See
base_rest_auth_jwt for an example.</p>
<p>In addition, authenticated_partner_id is available in record rule
evaluation context.</p>
</div>
<div class="section" id="known-issues-roadmap">
<h2><a class="toc-backref" href="#toc-entry-3">Known issues / Roadmap</a></h2>
<p>The
<a class="reference external" href="https://github.com/OCA/rest-framework/issues?q=is%3Aopen+is%3Aissue+label%3Aenhancement+label%3Abase_rest">roadmap</a>
and <a class="reference external" href="https://github.com/OCA/rest-framework/issues?q=is%3Aopen+is%3Aissue+label%3Abug+label%3Abase_rest">known
issues</a>
can be found on GitHub.</p>
</div>
<div class="section" id="changelog">
<h2><a class="toc-backref" href="#toc-entry-4">Changelog</a></h2>
<div class="section" id="section-1">
<h3><a class="toc-backref" href="#toc-entry-5">16.0.1.0.2 (2023-10-07)</a></h3>
<p><strong>Features</strong></p>
<ul class="simple">
<li>Add support for oauth2 security scheme in the Swagger UI. If your
openapi specification contains a security scheme of type oauth2, the
Swagger UI will display a login button in the top right corner. In
order to finalize the login process, a redirect URL must be provided
when initializing the Swagger UI. The Swagger UI is now initialized
with a oauth2RedirectUrl option that references a oauth2-redirect.html
file provided by the swagger-ui lib and served by the current addon.
(<a class="reference external" href="https://github.com/OCA/rest-framework/issues/379">#379</a>)</li>
</ul>
</div>
<div class="section" id="section-2">
<h3><a class="toc-backref" href="#toc-entry-6">12.0.2.0.1</a></h3>
<ul class="simple">
<li>validator_…() methods can now return a cerberus <tt class="docutils literal">Validator</tt>
object instead of a schema dictionnary, for additional flexibility
(e.g. allowing validator options such as <tt class="docutils literal">allow_unknown</tt>).</li>
</ul>
</div>
<div class="section" id="section-3">
<h3><a class="toc-backref" href="#toc-entry-7">12.0.2.0.0</a></h3>
<ul class="simple">
<li>Licence changed from AGPL-3 to LGPL-3</li>
</ul>
</div>
<div class="section" id="section-4">
<h3><a class="toc-backref" href="#toc-entry-8">12.0.1.0.1</a></h3>
<ul class="simple">
<li>Fix issue when rendering the jsonapi documentation if no documentation
is provided on a method part of the REST api.</li>
</ul>
</div>
<div class="section" id="section-5">
<h3><a class="toc-backref" href="#toc-entry-9">12.0.1.0.0</a></h3>
<p>First official version. The addon has been incubated into the
<a class="reference external" href="https://github.com/akretion/odoo-shopinvader">Shopinvader
repository</a> from
Akretion. For more information you need to look at the git log.</p>
</div>
</div>
<div class="section" id="bug-tracker">
<h2><a class="toc-backref" href="#toc-entry-10">Bug Tracker</a></h2>
<p>Bugs are tracked on <a class="reference external" href="https://github.com/OCA/rest-framework/issues">GitHub Issues</a>.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
<a class="reference external" href="https://github.com/OCA/rest-framework/issues/new?body=module:%20base_rest%0Aversion:%2018.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**">feedback</a>.</p>
<p>Do not contact contributors directly about support or help with technical issues.</p>
</div>
<div class="section" id="credits">
<h2><a class="toc-backref" href="#toc-entry-11">Credits</a></h2>
<div class="section" id="authors">
<h3><a class="toc-backref" href="#toc-entry-12">Authors</a></h3>
<ul class="simple">
<li>ACSONE SA/NV</li>
</ul>
</div>
<div class="section" id="contributors">
<h3><a class="toc-backref" href="#toc-entry-13">Contributors</a></h3>
<ul class="simple">
<li>Laurent Mignon &lt;<a class="reference external" href="mailto:laurent.mignon&#64;acsone.eu">laurent.mignon&#64;acsone.eu</a>&gt;</li>
<li>Sébastien Beau &lt;<a class="reference external" href="mailto:sebastien.beau&#64;akretion.com">sebastien.beau&#64;akretion.com</a>&gt;</li>
</ul>
</div>
<div class="section" id="maintainers">
<h3><a class="toc-backref" href="#toc-entry-14">Maintainers</a></h3>
<p>This module is maintained by the OCA.</p>
<a class="reference external image-reference" href="https://odoo-community.org">
<img alt="Odoo Community Association" src="https://odoo-community.org/logo.png" />
</a>
<p>OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.</p>
<p>This module is part of the <a class="reference external" href="https://github.com/OCA/rest-framework/tree/18.0/base_rest">OCA/rest-framework</a> project on GitHub.</p>
<p>You are welcome to contribute. To learn how please visit <a class="reference external" href="https://odoo-community.org/page/Contribute">https://odoo-community.org/page/Contribute</a>.</p>
</div>
</div>
</div>
</div>
</body>
</html>
