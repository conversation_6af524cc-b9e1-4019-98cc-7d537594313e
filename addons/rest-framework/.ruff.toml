
target-version = "py310"
fix = true

[lint]
extend-select = [
    "B",
    "C90",
    "E501",  # line too long (default 88)
    "I",  # isort
    "UP",  # pyupgrade
]
extend-safe-fixes = ["UP008"]
exclude = ["setup/*"]

[format]
exclude = ["setup/*"]

[lint.per-file-ignores]
"__init__.py" = ["F401", "I001"]  # ignore unused and unsorted imports in __init__.py
"__manifest__.py" = ["B018"]  # useless expression

[lint.isort]
section-order = ["future", "standard-library", "third-party", "odoo", "odoo-addons", "first-party", "local-folder"]

[lint.isort.sections]
"odoo" = ["odoo"]
"odoo-addons" = ["odoo.addons"]

[lint.mccabe]
max-complexity = 16
