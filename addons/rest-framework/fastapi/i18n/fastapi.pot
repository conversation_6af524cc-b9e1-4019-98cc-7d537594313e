# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* fastapi
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: fastapi
#: model:ir.model.fields,help:fastapi.field_fastapi_endpoint__description
msgid "A short description of the API. It can use Markdown"
msgstr ""

#. module: fastapi
#: model:ir.model.fields,field_description:fastapi.field_fastapi_endpoint__active
msgid "Active"
msgstr ""

#. module: fastapi
#: model:res.groups,name:fastapi.group_fastapi_manager
msgid "Administrator"
msgstr ""

#. module: fastapi
#: model:ir.model.fields.selection,name:fastapi.selection__fastapi_endpoint__demo_auth_method__api_key
msgid "Api Key"
msgstr ""

#. module: fastapi
#: model:ir.model.fields,field_description:fastapi.field_fastapi_endpoint__app
#: model_terms:ir.ui.view,arch_db:fastapi.fastapi_endpoint_search_view
msgid "App"
msgstr ""

#. module: fastapi
#: model_terms:ir.ui.view,arch_db:fastapi.fastapi_endpoint_form_view
#: model_terms:ir.ui.view,arch_db:fastapi.fastapi_endpoint_search_view
msgid "Archived"
msgstr ""

#. module: fastapi
#: model:ir.model.fields,field_description:fastapi.field_fastapi_endpoint__demo_auth_method
msgid "Authenciation method"
msgstr ""

#. module: fastapi
#: model:ir.model.fields,field_description:fastapi.field_fastapi_endpoint__company_id
msgid "Company"
msgstr ""

#. module: fastapi
#: model_terms:ir.ui.view,arch_db:fastapi.fastapi_endpoint_demo_form_view
msgid "Configuration"
msgstr ""

#. module: fastapi
#: model:ir.model.fields,field_description:fastapi.field_fastapi_endpoint__create_uid
msgid "Created by"
msgstr ""

#. module: fastapi
#: model:ir.model.fields,field_description:fastapi.field_fastapi_endpoint__create_date
msgid "Created on"
msgstr ""

#. module: fastapi
#: model:ir.model.fields.selection,name:fastapi.selection__fastapi_endpoint__app__demo
msgid "Demo Endpoint"
msgstr ""

#. module: fastapi
#: model:ir.model.fields,field_description:fastapi.field_fastapi_endpoint__description
msgid "Description"
msgstr ""

#. module: fastapi
#: model:ir.model.fields,field_description:fastapi.field_fastapi_endpoint__display_name
msgid "Display Name"
msgstr ""

#. module: fastapi
#: model:ir.model.fields,field_description:fastapi.field_fastapi_endpoint__docs_url
msgid "Docs Url"
msgstr ""

#. module: fastapi
#: model:ir.module.category,name:fastapi.module_category_fastapi
#: model:ir.ui.menu,name:fastapi.menu_fastapi_root
msgid "FastAPI"
msgstr ""

#. module: fastapi
#: model:ir.actions.act_window,name:fastapi.fastapi_endpoint_act_window
#: model:ir.model,name:fastapi.model_fastapi_endpoint
#: model:ir.ui.menu,name:fastapi.fastapi_endpoint_menu
msgid "FastAPI Endpoint"
msgstr ""

#. module: fastapi
#: model:res.groups,name:fastapi.group_fastapi_endpoint_runner
msgid "FastAPI Endpoint Runner"
msgstr ""

#. module: fastapi
#: model_terms:ir.ui.view,arch_db:fastapi.fastapi_endpoint_search_view
msgid "Group by..."
msgstr ""

#. module: fastapi
#: model:ir.model.fields.selection,name:fastapi.selection__fastapi_endpoint__demo_auth_method__http_basic
msgid "HTTP Basic"
msgstr ""

#. module: fastapi
#: model:ir.module.category,description:fastapi.module_category_fastapi
msgid "Helps you manage your Fastapi Endpoints"
msgstr ""

#. module: fastapi
#: model:ir.model.fields,field_description:fastapi.field_fastapi_endpoint__id
msgid "ID"
msgstr ""

#. module: fastapi
#: model:ir.model,name:fastapi.model_res_lang
msgid "Languages"
msgstr ""

#. module: fastapi
#: model:ir.model.fields,field_description:fastapi.field_fastapi_endpoint__write_uid
msgid "Last Updated by"
msgstr ""

#. module: fastapi
#: model:ir.model.fields,field_description:fastapi.field_fastapi_endpoint__write_date
msgid "Last Updated on"
msgstr ""

#. module: fastapi
#: model:res.groups,name:fastapi.my_demo_app_group
msgid "My Demo Endpoint Group"
msgstr ""

#. module: fastapi
#: model:ir.model.fields,field_description:fastapi.field_fastapi_endpoint__name
msgid "Name"
msgstr ""

#. module: fastapi
#: model:ir.model.fields,help:fastapi.field_fastapi_endpoint__registry_sync
msgid ""
"ON: the record has been modified and registry was not notified.\n"
"No change will be active until this flag is set to false via proper action.\n"
"\n"
"OFF: record in line with the registry, nothing to do."
msgstr ""

#. module: fastapi
#: model:ir.model.fields,field_description:fastapi.field_fastapi_endpoint__openapi_url
msgid "Openapi Url"
msgstr ""

#. module: fastapi
#: model:ir.model,name:fastapi.model_ir_rule
msgid "Record Rule"
msgstr ""

#. module: fastapi
#: model:ir.model.fields,field_description:fastapi.field_fastapi_endpoint__redoc_url
msgid "Redoc Url"
msgstr ""

#. module: fastapi
#: model:ir.model.fields,field_description:fastapi.field_fastapi_endpoint__registry_sync
msgid "Registry Sync"
msgstr ""

#. module: fastapi
#: model_terms:ir.ui.view,arch_db:fastapi.fastapi_endpoint_form_view
msgid "Registry Sync Required"
msgstr ""

#. module: fastapi
#: model:ir.model.fields,field_description:fastapi.field_fastapi_endpoint__root_path
msgid "Root Path"
msgstr ""

#. module: fastapi
#: model:ir.model.fields,field_description:fastapi.field_fastapi_endpoint__save_http_session
msgid "Save HTTP Session"
msgstr ""

#. module: fastapi
#: model:ir.actions.server,name:fastapi.fastapi_endpoint_action_sync_registry
#: model_terms:ir.ui.view,arch_db:fastapi.fastapi_endpoint_form_view
#: model_terms:ir.ui.view,arch_db:fastapi.fastapi_endpoint_tree_view
msgid "Sync Registry"
msgstr ""

#. module: fastapi
#. odoo-python
#: code:addons/fastapi/models/fastapi_endpoint_demo.py:0
msgid "The authentication method is required for app %(app)s"
msgstr ""

#. module: fastapi
#: model:ir.model.fields,help:fastapi.field_fastapi_endpoint__name
msgid "The title of the API."
msgstr ""

#. module: fastapi
#: model:ir.model.fields,help:fastapi.field_fastapi_endpoint__user_id
msgid "The user to use to execute the API calls."
msgstr ""

#. module: fastapi
#: model:ir.model.fields,field_description:fastapi.field_fastapi_endpoint__user_id
#: model:res.groups,name:fastapi.group_fastapi_user
msgid "User"
msgstr ""

#. module: fastapi
#: model:ir.model.fields,help:fastapi.field_fastapi_endpoint__save_http_session
msgid ""
"Whether session should be saved into the session store. This is required if "
"for example you use the Odoo's authentication mechanism. Oherwise chance are"
" high that you don't need it and could turn off this behaviour. Additionaly "
"turning off this option will prevent useless IO operation when storing and "
"reading the session on the disk and prevent unexpecteed disk space "
"consumption."
msgstr ""

#. module: fastapi
#. odoo-python
#: code:addons/fastapi/models/fastapi_endpoint.py:0
msgid "`%(name)s` uses a blacklisted root_path = `%(root_path)s`"
msgstr ""
