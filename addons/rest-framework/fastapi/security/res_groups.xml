<?xml version="1.0" encoding="utf-8" ?>
<!-- Copyright 2022 ACSONE SA/NV
     License LGPL-3.0 or later (http://www.gnu.org/licenses/LGPL). -->
<odoo>
    <record model="ir.module.category" id="module_category_fastapi">
        <field name="name">FastAPI</field>
        <field name="description">Helps you manage your Fastapi Endpoints</field>
        <field name="sequence">99</field>
    </record>

    <record id="group_fastapi_user" model="res.groups">
        <field name="name">User</field>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]" />
        <field name="category_id" ref="module_category_fastapi" />
    </record>

    <record id="group_fastapi_manager" model="res.groups">
        <field name="name">Administrator</field>
        <field name="category_id" ref="module_category_fastapi" />
        <field name="implied_ids" eval="[(4, ref('group_fastapi_user'))]" />
        <field
            name="users"
            eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"
        />
    </record>

    <!-- create a basic group providing the minimal access rights to retrieve
     the user running the endpoint handlers and performs authentication -->
    <record id="group_fastapi_endpoint_runner" model="res.groups">
        <field name="name">FastAPI Endpoint Runner</field>
        <field name="category_id" ref="module_category_fastapi" />
    </record>
</odoo>
