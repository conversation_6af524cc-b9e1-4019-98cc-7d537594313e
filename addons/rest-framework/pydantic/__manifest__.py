# Copyright 2021 ACSONE SA/NV
# License LGPL-3.0 or later (http://www.gnu.org/licenses/lgpl.html)

{
    "name": "Pydantic",
    "summary": """
        Utility addon to ease mapping between Pydantic and Odoo models""",
    "version": "18.0.1.0.1",
    "development_status": "Beta",
    "license": "LGPL-3",
    "maintainers": ["lmignon"],
    "author": "ACSONE SA/NV,Odoo Community Association (OCA)",
    "website": "https://github.com/OCA/rest-framework",
    "depends": [],
    "data": [],
    "demo": [],
    "external_dependencies": {
        "python": ["pydantic>=2.0.0", "contextvars", "typing-extensions"]
    },
    "installable": True,
}
