# Copyright 2020 Camptocamp SA (http://www.camptocamp.com)
# <AUTHOR> <<EMAIL>>

from odoo import exceptions as odoo_exceptions


class RESTServiceDispatchException(Exception):
    rest_json_info = {}

    def __init__(self, message, log_entry_url):
        super().__init__(message)
        self.rest_json_info = {"log_entry_url": log_entry_url}


class RESTServiceMissingErrorException(
    RESTServiceDispatchException, odoo_exceptions.MissingError
):
    """Missing error wrapped exception."""


class RESTServiceUserErrorException(
    RESTServiceDispatchException, odoo_exceptions.UserError
):
    """User error wrapped exception."""


class RESTServiceValidationErrorException(
    RESTServiceDispatchException, odoo_exceptions.ValidationError
):
    """Validation error wrapped exception."""
