# Copyright 2022 ACSONE SA/NV
# License LGPL-3.0 or later (http://www.gnu.org/licenses/LGPL).

from odoo import models, fields, api


class EndpointRouteSyncMixin(models.AbstractModel):
    """
    Minimal implementation of endpoint route sync mixin
    to replace the missing endpoint_route_handler dependency
    """
    _name = 'endpoint.route.sync.mixin'
    _description = 'Endpoint Route Sync Mixin'

    # Basic fields needed for endpoint management
    registry_sync = fields.Boolean(
        string='Registry Sync',
        default=True,
        help='Whether this endpoint is synced with the registry'
    )
    
    # Abstract methods that need to be implemented by inheriting models
    def _prepare_endpoint_rules(self, options=None):
        """Prepare endpoint rules - to be implemented by inheriting models"""
        return []
    
    def _registered_endpoint_rule_keys(self):
        """Get registered endpoint rule keys - to be implemented by inheriting models"""
        return tuple()
    
    @api.model
    def _routing_impacting_fields(self):
        """Fields that impact routing - to be implemented by inheriting models"""
        return tuple()
    
    # Registry management
    @property
    def _endpoint_registry(self):
        """Simple endpoint registry placeholder"""
        return self.env.registry
    
    def _endpoint_registry_route_unique_key(self, routing):
        """Generate unique key for route"""
        routes = routing.get('routes', [])
        route_str = '|'.join(routes) if routes else ''
        return f"{routing.get('type', 'http')}:{route_str}"
