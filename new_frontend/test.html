<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🧪 Odoo API Test Page</h1>
    
    <div id="results"></div>
    
    <button onclick="testConnection()">Test Connection</button>
    <button onclick="testEmployees()">Test Load Employees</button>
    <button onclick="testAttendances()">Test Load Attendances</button>
    <button onclick="clearResults()">Clear Results</button>
    
    <script src="odoo-api.js"></script>
    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testConnection() {
            addResult('🔌 Testing connection...', 'info');
            
            try {
                const result = await odooAPI.connect(
                    'http://localhost:10017',
                    'attendance_test',
                    '<EMAIL>',
                    '12345'
                );
                
                if (result.success) {
                    const mode = result.mockMode ? 'Mock Data Mode' : 'Real Odoo Connection';
                    addResult(`✅ Connection successful! (${mode})`, 'success');
                    addResult(`<pre>${JSON.stringify(result, null, 2)}</pre>`, 'info');
                } else {
                    addResult(`❌ Connection failed: ${result.error}`, 'error');
                }
            } catch (error) {
                addResult(`💥 Connection error: ${error.message}`, 'error');
            }
        }
        
        async function testEmployees() {
            addResult('👥 Testing employee loading...', 'info');
            
            try {
                // Test search
                const employeeIds = await odooAPI.search('hr.employee');
                addResult(`📋 Found ${employeeIds.length} employee IDs: ${JSON.stringify(employeeIds)}`, 'success');
                
                // Test read
                const employees = await odooAPI.read('hr.employee', employeeIds, [
                    'name', 'barcode', 'work_email', 'department_id'
                ]);
                addResult(`📊 Loaded ${employees.length} employees:`, 'success');
                addResult(`<pre>${JSON.stringify(employees, null, 2)}</pre>`, 'info');
                
            } catch (error) {
                addResult(`❌ Employee loading failed: ${error.message}`, 'error');
                console.error('Employee loading error:', error);
            }
        }
        
        async function testAttendances() {
            addResult('⏰ Testing attendance loading...', 'info');
            
            try {
                // Test search
                const attendanceIds = await odooAPI.search('hr.attendance');
                addResult(`📋 Found ${attendanceIds.length} attendance IDs: ${JSON.stringify(attendanceIds)}`, 'success');
                
                // Test read
                const attendances = await odooAPI.read('hr.attendance', attendanceIds, [
                    'employee_id', 'check_in', 'check_out', 'worked_hours'
                ]);
                addResult(`📊 Loaded ${attendances.length} attendance records:`, 'success');
                addResult(`<pre>${JSON.stringify(attendances, null, 2)}</pre>`, 'info');
                
            } catch (error) {
                addResult(`❌ Attendance loading failed: ${error.message}`, 'error');
                console.error('Attendance loading error:', error);
            }
        }
        
        // Auto-run connection test on page load
        window.addEventListener('load', function() {
            addResult('🚀 Page loaded. Click "Test Connection" to start.', 'info');
        });
    </script>
</body>
</html>
