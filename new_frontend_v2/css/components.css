/**
 * Component-specific CSS for Extended Attendance System
 * Styles for specific UI components and widgets
 */

/* Login Section */
.login-section {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    padding: var(--spacing-lg);
}

.login-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-xxl);
    width: 100%;
    max-width: 400px;
}

.login-card h2 {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    color: var(--text-primary);
}

.connection-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--danger-color);
    transition: background-color var(--transition-fast);
}

.status-indicator.connected {
    background: var(--success-color);
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    border-left: 4px solid var(--primary-color);
    transition: transform var(--transition-fast);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-card.success {
    border-left-color: var(--success-color);
}

.stat-card.warning {
    border-left-color: var(--warning-color);
}

.stat-card.danger {
    border-left-color: var(--danger-color);
}

.stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.stat-title {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
    margin: 0;
}

.stat-icon {
    font-size: var(--font-size-lg);
    color: var(--primary-color);
}

.stat-value {
    font-size: var(--font-size-xxl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.stat-change {
    font-size: var(--font-size-xs);
    margin-top: var(--spacing-sm);
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--danger-color);
}

/* Data Tables */
.data-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.data-table th,
.data-table td {
    padding: var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.data-table th {
    background: var(--bg-secondary);
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.data-table tbody tr:hover {
    background: var(--bg-secondary);
}

.data-table tbody tr:last-child td {
    border-bottom: none;
}

/* Status Badges */
.badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-success {
    background: rgba(46, 204, 113, 0.1);
    color: var(--success-color);
}

.badge-warning {
    background: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
}

.badge-danger {
    background: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
}

.badge-info {
    background: rgba(52, 152, 219, 0.1);
    color: var(--primary-color);
}

.badge-secondary {
    background: rgba(108, 117, 125, 0.1);
    color: var(--secondary-color);
}

/* Person Cards */
.person-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-lg);
}

.person-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: transform var(--transition-fast);
    cursor: pointer;
}

.person-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.person-card-header {
    padding: var(--spacing-lg);
    text-align: center;
    background: var(--bg-secondary);
}

.person-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin: 0 auto var(--spacing-md);
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.person-name {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin: 0 0 var(--spacing-sm);
}

.person-type {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.person-card-body {
    padding: var(--spacing-lg);
}

.person-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--danger-color);
}

.status-dot.online {
    background: var(--success-color);
}

/* Location Cards */
.location-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: var(--spacing-lg);
}

.location-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: transform var(--transition-fast);
}

.location-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.location-card-header {
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
}

.location-name {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin: 0 0 var(--spacing-sm);
}

.location-code {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-family: monospace;
}

.location-card-body {
    padding: var(--spacing-lg);
}

.occupancy-bar {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-sm);
    height: 8px;
    overflow: hidden;
    margin: var(--spacing-sm) 0;
}

.occupancy-fill {
    height: 100%;
    background: var(--success-color);
    transition: width var(--transition-normal);
}

.occupancy-fill.medium {
    background: var(--warning-color);
}

.occupancy-fill.high {
    background: var(--danger-color);
}

.occupancy-text {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    display: flex;
    justify-content: space-between;
}

/* Modals */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform var(--transition-normal);
}

.modal-overlay.show .modal {
    transform: scale(1);
}

.modal-sm { width: 400px; }
.modal-md { width: 600px; }
.modal-lg { width: 800px; }
.modal-xl { width: 1000px; }

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    cursor: pointer;
    color: var(--text-secondary);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    transition: background-color var(--transition-fast);
}

.modal-close:hover {
    background: var(--bg-secondary);
}

.modal-body {
    padding: var(--spacing-lg);
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
}

/* Toast Notifications */
.toast {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-md) var(--spacing-lg);
    border-left: 4px solid var(--primary-color);
    z-index: 1100;
    transform: translateX(100%);
    transition: transform var(--transition-normal);
    max-width: 400px;
}

.toast.show {
    transform: translateX(0);
}

.toast-success {
    border-left-color: var(--success-color);
}

.toast-warning {
    border-left-color: var(--warning-color);
}

.toast-error {
    border-left-color: var(--danger-color);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.toast-content i {
    font-size: var(--font-size-lg);
}

/* Loading Spinner */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
}

.loading-spinner {
    text-align: center;
    color: var(--text-secondary);
}

.loading-spinner i {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-md);
}

/* Search and Filters */
.search-filters {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

.search-row {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: var(--spacing-md);
    align-items: end;
}

.filter-group {
    display: flex;
    gap: var(--spacing-md);
    align-items: end;
}

/* Quick Actions */
.quick-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
}

.action-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--text-primary);
    transition: all var(--transition-fast);
    cursor: pointer;
}

.action-button:hover {
    background: var(--bg-secondary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.action-button i {
    font-size: var(--font-size-lg);
}

/* Responsive Design */
@media (max-width: 768px) {
    .app-container {
        grid-template-areas: 
            "header"
            "main";
        grid-template-columns: 1fr;
        grid-template-rows: var(--header-height) 1fr;
    }
    
    .app-sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: var(--sidebar-width);
        height: 100vh;
        transition: left var(--transition-normal);
        z-index: 1001;
    }
    
    .app-sidebar.show {
        left: 0;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .person-grid,
    .location-grid {
        grid-template-columns: 1fr;
    }
    
    .search-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .quick-actions {
        justify-content: center;
    }
    
    .modal {
        width: 95vw !important;
        margin: var(--spacing-md);
    }
}
